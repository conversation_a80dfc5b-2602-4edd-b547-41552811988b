﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Skybridge.Domain.Core.Model;

public interface IScheduledTaskInfo
{
    /// <summary>
    /// 绑定的项目ID
    /// </summary>
    string ProjectId { get; }
    
    /// <summary>
    /// 任务唯一标识
    /// </summary>
    string TaskId { get; }

    /// <summary>
    /// 任务名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 任务描述
    /// </summary>
    string Description { get; }

    /// <summary>
    /// 调度规则
    /// </summary>
    IScheduleRule ScheduleRule { get; }
    
    ScheduleType ScheduleType { get; }
    

    /// <summary>
    /// 是否启用
    /// </summary>
    bool IsEnabled { get; }

    /// <summary>
    /// 上一次执行时间
    /// </summary>
    DateTime? LastExecutionTime { get; }

    /// <summary>
    /// 下一次执行时间
    /// </summary>
    DateTime? NextExecutionTime { get; }
    
    /// <summary>
    /// 运行项目是否记录调试日志
    /// </summary>
    public bool IsLogDebug { get; }
    
    /// <summary>
    /// 运行项目的参数
    /// </summary>
    /// <returns></returns>
    public Dictionary<string, string>? KvbParameters { get; }

}