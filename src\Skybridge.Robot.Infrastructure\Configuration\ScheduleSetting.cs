﻿using Newtonsoft.Json;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Model;
using Skybridge.Robot.Infrastructure.Models;

namespace Skybridge.Robot.Infrastructure.Configuration;

public class ScheduleSetting
{
        public Dictionary<string, ScheduleTaskDto> ScheduledTaskInfos = new Dictionary<string, ScheduleTaskDto>();

        // 默认配置文件路径（用户应用数据目录）
        private readonly string _defaultFilePath;

        public ScheduleSetting()
        {
            
        }
        
        // 构造函数，初始化默认路径
        public ScheduleSetting(IFileStorageConfig fileStorageConfig)
        {
            // 创建应用专属目录
            string appFolder = fileStorageConfig.BasePath;
            Directory.CreateDirectory(appFolder);
            // 设置默认配置文件路径
            _defaultFilePath = Path.Combine(appFolder, "scheduleSetting.json");
            LoadSettings();
        }

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <returns>是否保存成功</returns>
        public bool SaveSettings()
        {
            return SaveSettings(_defaultFilePath);
        }

        /// <summary>
        /// 保存配置到指定文件
        /// </summary>
        /// <param name="filePath">目标文件路径</param>
        /// <returns>是否保存成功</returns>
        public bool SaveSettings(string filePath)
        {
            try
            {
                // 序列化配置数据
                string jsonData = JsonConvert.SerializeObject(this);

                // 写入文件
                File.WriteAllText(filePath, jsonData);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($@"保存配置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从默认路径加载配置
        /// </summary>
        /// <returns>是否加载成功</returns>
        public bool LoadSettings()
        {
            return LoadSettings(_defaultFilePath);
        }

        /// <summary>
        /// 从指定路径加载配置
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>是否加载成功</returns>
        public bool LoadSettings(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Console.WriteLine(@"配置文件不存在，将使用默认配置");
                    return false;
                }

                // 读取文件内容
                string jsonData = File.ReadAllText(filePath);

                var loadedSettings = JsonConvert.DeserializeObject<ScheduleSetting>(jsonData);

                // 更新当前实例的配置数据
                if (loadedSettings != null)
                {
                    ScheduledTaskInfos = new Dictionary<string, ScheduleTaskDto>(loadedSettings.ScheduledTaskInfos);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($@"加载配置失败: {ex.Message}");
                return false;
            }
        }
}