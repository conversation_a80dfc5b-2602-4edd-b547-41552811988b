using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Skybridge.Domain.Core.Model;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Models;

namespace Skybridge.Robot.Presentation.Services;

/// <summary>
/// 调度功能测试服务
/// </summary>
public class SchedulingTestService
{
    private readonly IScheduleManager _scheduleManager;
    private readonly ILogger<SchedulingTestService>? _logger;

    public SchedulingTestService(IScheduleManager scheduleManager, ILogger<SchedulingTestService>? logger = null)
    {
        _scheduleManager = scheduleManager;
        _logger = logger;
    }

    /// <summary>
    /// 测试基本调度功能
    /// </summary>
    public async Task<bool> TestBasicSchedulingAsync(bool isLogDebug,Dictionary<string,string>? kvbParameters=null)
    {
        try
        {
            _logger?.LogInformation("开始测试基本调度功能");

            // 测试1: 创建一个简单的间隔调度任务
            var intervalRule = ScheduleRule.CreateInterval(5); // 每5秒执行一次
            var testTask = new ScheduledTaskInfo("test_interval",
                "test_interval_task",
                "测试间隔任务",
                "每5秒执行一次的测试任务",
                intervalRule,
                ScheduleType.Interval,
                isLogDebug,
                kvbParameters
            );

            // 添加任务到调度管理器
            var taskId = _scheduleManager.AddTask(testTask);
            _logger?.LogInformation($"添加测试任务成功，任务ID: {taskId}");

            // 启动调度管理器
            _scheduleManager.StartManager();
            _logger?.LogInformation("调度管理器已启动");

            // 等待一段时间观察执行
            await Task.Delay(15000); // 等待15秒

            // 停止并清理
            _scheduleManager.RemoveTask(taskId);
            _scheduleManager.StopManager();
            
            _logger?.LogInformation("基本调度功能测试完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "基本调度功能测试失败");
            return false;
        }
    }

    /// <summary>
    /// 测试Cron表达式调度
    /// </summary>
    public async Task<bool> TestCronSchedulingAsync(bool isLogDebug,Dictionary<string,string>? kvbParameters=null)
    {
        try
        {
            _logger?.LogInformation("开始测试Cron表达式调度功能");

            // 创建一个每分钟执行的Cron任务
            var cronRule = new ScheduleRule("0 * * * *"); // 每分钟的第0秒执行
            var cronTask = new ScheduledTaskInfo("test_cron",
                "test_cron_task",
                "测试Cron任务",
                "每分钟执行一次的Cron测试任务",
                cronRule,
                ScheduleType.Cron,
                isLogDebug,
                kvbParameters
                
            );

            var taskId = _scheduleManager.AddTask(cronTask);
            _logger?.LogInformation($"添加Cron测试任务成功，任务ID: {taskId}");

            // 验证下次执行时间计算
            var nextExecution = cronRule.GetNextExecutionTime();
            _logger?.LogInformation($"下次执行时间: {nextExecution?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}");

            // 清理
            _scheduleManager.RemoveTask(taskId);
            
            _logger?.LogInformation("Cron表达式调度功能测试完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Cron表达式调度功能测试失败");
            return false;
        }
    }

    /// <summary>
    /// 测试调度规则验证
    /// </summary>
    public bool TestScheduleRuleValidation(bool isLogDebug,Dictionary<string,string>? kvbParameters=null)
    {
        try
        {
            _logger?.LogInformation("开始测试调度规则验证功能");

            // 测试有效的Cron表达式
            var validCronExpressions = new[]
            {
                "0 0 * * *",     // 每天午夜
                "0 */6 * * *",   // 每6小时
                "30 2 * * 1",    // 每周一凌晨2:30
                "0 0 1 * *"      // 每月1号午夜
            };

            foreach (var cronExpr in validCronExpressions)
            {
                var isValid = ScheduleRule.IsValidCronExpression(cronExpr);
                _logger?.LogInformation($"Cron表达式 '{cronExpr}' 验证结果: {(isValid ? "有效" : "无效")}");
                
                if (isValid)
                {
                    var rule = new ScheduleRule(cronExpr);
                    var nextTime = rule.GetNextExecutionTime();
                    _logger?.LogInformation($"  下次执行时间: {nextTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}");
                }
            }

            // 测试无效的Cron表达式
            var invalidCronExpressions = new[]
            {
                "",
                "invalid",
                "60 0 * * *",    // 无效分钟
                "0 25 * * *"     // 无效小时
            };

            foreach (var cronExpr in invalidCronExpressions)
            {
                var isValid = ScheduleRule.IsValidCronExpression(cronExpr);
                _logger?.LogInformation($"Cron表达式 '{cronExpr}' 验证结果: {(isValid ? "有效" : "无效")}");
            }

            // 测试间隔调度规则
            var intervalRule = ScheduleRule.CreateInterval(60); // 每60秒
            var dailyRule = ScheduleRule.CreateDaily(9, 30);    // 每天9:30
            var hourlyRule = ScheduleRule.CreateHourly(15);     // 每小时第15分钟

            _logger?.LogInformation($"间隔规则: {intervalRule}");
            _logger?.LogInformation($"每日规则: {dailyRule}");
            _logger?.LogInformation($"每小时规则: {hourlyRule}");

            _logger?.LogInformation("调度规则验证功能测试完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "调度规则验证功能测试失败");
            return false;
        }
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public async Task<bool> RunAllTestsAsync(bool isLogDebug,Dictionary<string,string>? kvbParameters=null)
    {
        _logger?.LogInformation("开始运行所有调度功能测试");

        var validationTest = TestScheduleRuleValidation(isLogDebug,kvbParameters);
        var cronTest = await TestCronSchedulingAsync(isLogDebug,kvbParameters);
        var basicTest = await TestBasicSchedulingAsync(isLogDebug,kvbParameters);

        var allPassed = validationTest && cronTest && basicTest;
        
        _logger?.LogInformation($"所有测试完成，结果: {(allPassed ? "通过" : "失败")}");
        return allPassed;
    }
}
