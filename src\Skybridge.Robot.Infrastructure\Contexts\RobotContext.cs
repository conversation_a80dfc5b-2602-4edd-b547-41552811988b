using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Skybridge.Activities.Private;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Enums;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Robot.Responses;
using Skybridge.Robot.Application.DTOs.Task.Requests;
using Skybridge.Robot.Application.DTOs.Task.Responses;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Models;
using Skybridge.Robot.Infrastructure.Utilities;
using Skybridge.Robot.Infrastructure.WebSocket.Models;
using Skybridge.Robot.Utils;

namespace Skybridge.Robot.Infrastructure.Contexts;

/// <summary>
/// 机器人上下文，管理机器人的生命周期、状态和通信
/// </summary>
public class RobotContext : IDisposable
{
    private readonly IExecManager _execManager;
    private readonly IBusinessRequestService _businessRequestService;
    private readonly IFileStorageConfig _fileStorageConfig;
    private readonly int LoopTime = 10;
    private readonly RunningContext _runningContext;
    private readonly ILogService _logService;
    private readonly IWebSocketClient _webSocketClient;
    private CancellationTokenSource _monitorCts;
    private readonly EnvironmentConfig _environmentConfig;
    private CancellationTokenSource? _webSocketReconnectCts;
    private Task? _webSocketReconnectTask;
    private readonly ConcurrentDictionary<string, Task<bool>> _activeDownloads = new ConcurrentDictionary<string, Task<bool>>();
    private CancellationTokenSource? _pollingCancellationTokenSource;

    private bool _isBusy;
    private readonly IAppConfigManager _appConfigManager;
    private readonly RobotConfig _robotConfig;

    public bool IsBusy
    {
        get { return _isBusy; }
        set
        {
            if (_isBusy != value)
            {
                _isBusy = value;
                OnBusyChanged?.Invoke(_isBusy);
            }
        }
    }

    #region 机器人属性
    public bool IsRemote => _appConfigManager.RobotConfig.IsRemote;
    public string Id => _appConfigManager.RobotConfig.RobotId;
    private readonly ConcurrentDictionary<string, Task<bool>> _activePackageDownloads =
        new ConcurrentDictionary<string, Task<bool>>();
    #endregion
    public event Action<bool> OnServerStateChanged;
    private bool _isPolling;

    private CancellationTokenSource _reportStateCts;
    private readonly ILostDataRepository _lostDataRepository;
    private readonly WorkingTaskManager _workingTaskManager;
    private readonly IRobotLogService _robotLogService;
    private readonly RemoteLog _remoteLog;
    private readonly IRobotWebServer _robotWebServer;
    private readonly CommonConfig _commonConfig;
    private readonly IUploadTaskRepository _uploadTaskRepository;
    private readonly IScheduleManager _scheduleManager;
    private readonly ScheduleSetting _scheduleSetting;
    private bool _isServerAvailable;

    public bool IsServerAvailable
    {
        get => _isServerAvailable;
        private set
        {
            if (_isServerAvailable != value)
            {
                _isServerAvailable = value;
                OnServerStateChanged?.Invoke(_isServerAvailable);
            }
        }
    }

    public Action<bool>? OnBusyChanged { get; set; }

    public RobotContext(
        IExecManager execManager,
        IBusinessRequestService businessRequestService,
        IFileStorageConfig fileStorageConfig,
        RunningContext runningContext,
        IAppConfigManager appConfigManager,
        ILogService logService,
        IWebSocketClient webSocketClient,
        EnvironmentConfig environmentConfig,
        ILostDataRepository lostDataRepository,
        WorkingTaskManager workingTaskManager,
        IRobotLogService robotLogService,
        RemoteLog remoteLog,
        IRobotWebServer robotWebServer,
        CommonConfig commonConfig,
        IUploadTaskRepository uploadTaskRepository,
        IScheduleManager scheduleManager)
    {
        
        _execManager = execManager;
        _logService = logService;
        _fileStorageConfig = fileStorageConfig;
        _runningContext = runningContext;
        _businessRequestService = businessRequestService;
        _webSocketClient = webSocketClient;
        _environmentConfig = environmentConfig;
        _appConfigManager = appConfigManager;
        _robotConfig = appConfigManager.RobotConfig;
        _lostDataRepository = lostDataRepository;
        _workingTaskManager = workingTaskManager;
        _robotLogService = robotLogService;
        _remoteLog = remoteLog;
        _robotWebServer = robotWebServer;
        _commonConfig = commonConfig;
        _uploadTaskRepository = uploadTaskRepository;
        _scheduleManager = scheduleManager;
        runningContext.OnTaskCompleted += OnTaskCompleted;
        runningContext.UploadTasksCountChanged += OnUploadTasksCountChanged;
        // 注册事件处理程序
        _ = RegisterEventHandlers();
        _ = ResetUploadTaskRepository();
        OnBusyChanged += OnBusyChangedHandler;
        runningContext.OnTaskLogEndUpdated += async void (task, message) =>
        {
            if (task == null)
            {
                return;
            }
            _remoteLog.AddLog(task.Id, $"组件日志发送完成,日志内容:{message}", OperationResult.Success, OperationType.End);
            //收到logEnd等待几秒钟后再退出,因为执行器有可能会自动退出
            await Task.Delay(5000);
            _execManager.StopExecutorAsync(task);
        };
        StartWorkers();
        
        appConfigManager.OnChangeMode += b =>
        {
            if (b)
            {
                StartWorkers();
            }
            else
            {
                StopWorkers();
            }
        };

        scheduleManager.ScheduleStartTask += ScheduleStartTask;


    }

    private async void OnUploadTasksCountChanged(int obj)
    {
        await UploadTasks();
    }

    private async void OnTaskCompleted(RobotTask task)
    {

        if (task.IsServerTask())
        {
            _remoteLog.AddLog(task.Id, $"执行任务", OperationResult.Success, OperationType.End);
        }
        _logService.LogInfo($"任务已结束: {task.Id}");
        var uploadTask = _runningContext.UploadRobotTaskDict.FirstOrDefault(d => d.Key
            .Equals(task.Id));
        if (uploadTask.Value != null)
        {
            uploadTask.Value.TaskCompletedTime.Restart();
        }

        if (task.StopType == 1)
        {
            TaskLog taskLog = new TaskLog()
            {
                TaskId = task.Id,
                Message = "主动停止",
                Status = "Fail",
                traceInfo = null
            };
            await _robotLogService.UploadTaskLog(taskLog);
        }
        else if (task.StopType == 2)
        {
            TaskLog taskLog = new TaskLog()
            {
                TaskId = task.Id,
                Message = task.StopMessage,
                Status = "Fail",
                traceInfo = null
            };
            await _robotLogService.UploadTaskLog(taskLog);
        }
    }

    #region  单机调度触发

    private async void ScheduleStartTask(IScheduledTaskInfo info)
    {
        try
        {
            var taskId = Guid.NewGuid().ToString();
            var robotProject = _runningContext.RobotProjects.Find(d => d.Id.Equals(info.ProjectId));
            if (robotProject == null)
            {
                return;
            }
            TaskInfo taskInfo = new TaskInfo()
            {
                Category = robotProject.Category,
                Code = robotProject.Code.ToLower(),
                Description = robotProject.Description,
                IsVideoLog = false,
                LogType = info.IsLogDebug ? "Debug" : "Info",
                ProjectRoot = robotProject.FilePath,
                ProjectName = robotProject.Name,
                Parameters = JsonConvert.SerializeObject(info.KvbParameters),
                ProjectId = robotProject.Id,
                TaskId = taskId,
                Version = robotProject.Version
            };
            await LocalRunTask(taskInfo);
        }
        catch (Exception e)
        {
            _logService.LogError($"单机调度执行任务发生错误:{e.ToString()}");
        }
    }

    #endregion

    #region Workers

    private void StartWorkers()
    {
        if (IsRemote)
        {
            _workingTaskManager.StartLongRunningTask(WorkerConst.ReportStateWorker, ReportState, "上报执行器状态", 60);
            _workingTaskManager.StartLongRunningTask(WorkerConst.GetOperationWorker, GetOperation, "获取操作", 120);
            _workingTaskManager.StartLongRunningTask(WorkerConst.ServerStateMonitorWorker,ServerStateCheck,"心跳",60);
            _workingTaskManager.StartLongRunningTask(WorkerConst.WorkStatusUploadWorker, WorkStatusUpload, "工作监控",
                60);
            _workingTaskManager.StartLongRunningTask(WorkerConst.PollingTaskWorker, PollingTask, "轮询任务", LoopTime);
            _workingTaskManager.StartLongRunningTask(WorkerConst.StopTaskWorker, StopTask, "停止任务", 120);
            _workingTaskManager.StartLongRunningTask(WorkerConst.CleanUploadTaskWorker, CleanUploadTask, "清理任务日志",
                5);
        }
    }

    private void StopWorkers()
    {
        _workingTaskManager.StopLongRunningTask(WorkerConst.ReportStateWorker);
        _workingTaskManager.StopLongRunningTask(WorkerConst.GetOperationWorker);
        _workingTaskManager.StopLongRunningTask(WorkerConst.ServerStateMonitorWorker);
        _workingTaskManager.StopLongRunningTask(WorkerConst.WorkStatusUploadWorker);
    }
    public WorkResult PollingTask()
    {
        try
        {
            if (_isPolling)
            {
                return new WorkResult(false, "已经在拉取任务过程中了");
            }

            _isPolling = true;
            var count = _runningContext.UploadRobotTaskDict.Count;
            if (!IsRemote || count >= _robotConfig.MaxRunningCount)
            {
                return new WorkResult(false, $"不满足轮询条件,当前正在运行的任务数量:{count}");
            }

            var taskInfo = _businessRequestService.GetTaskAsync(_robotConfig.HttpUrl).Result;
            if (taskInfo != null)
            {
                var runTaskRes = RunTask(taskInfo).Result;
                return runTaskRes;
            }

            return new WorkResult(false, $"轮询未获取到任务");
        }
        catch (Exception ex)
        {
            return new WorkResult(false, $"拉取任务时发生异常:{ex.ToString()}");
        }
        finally
        {
            _isPolling = false;
        }
    }
    
    public WorkResult StopTask()
    {
        return StopTaskAsync().Result;
    }
    
    
    private WorkResult CleanUploadTask()
{
    StringBuilder tasks = new StringBuilder();
    StringBuilder errorTasks = new StringBuilder();
    StringBuilder noLogTasks = new StringBuilder();
    foreach (var uploadTask in _runningContext.UploadRobotTaskDict.Values)
    {
        if (!string.IsNullOrEmpty(uploadTask.TaskLogContent))
        {
            var res = _businessRequestService.UploadTaskLogAsync(_robotConfig.HttpUrl, uploadTask.TaskLogContent).Result;
            if (res.IsSuccess)
            {
                tasks.Append(uploadTask.Id);
                tasks.Append(",");
                _runningContext.RemoveUploadTask(uploadTask.Id);
            }
            else
            {
                errorTasks.Append(uploadTask.Id);
                errorTasks.Append(",");
            }
        }
        else
        {
            noLogTasks.Append(uploadTask.Id);
            noLogTasks.Append(",");
            if (uploadTask.TaskCompletedTime.ElapsedMilliseconds > 30 * 1000)
            {
                TaskLogModel taskLogModel = new TaskLogModel()
                {
                    State = 2,
                    RpaId = _robotConfig.RobotId,
                    Task_Id = uploadTask.Id,
                    Details = "引擎退出后超时未收到任务日志",
                    SignGuid = Guid.NewGuid().ToString(),
                    UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")
                };
                uploadTask.TaskLogContent = JsonConvert.SerializeObject(taskLogModel);
            }
        }
    }

    string message = "";
    bool isSuccess = true;
    if (!string.IsNullOrEmpty(tasks.ToString()))
    {
        message = $"任务日志补上传成功:{tasks.ToString()}";
    }
    if (!string.IsNullOrEmpty(noLogTasks.ToString()))
    {
        message += $"以下任务还未收到任务日志:{noLogTasks.ToString()}";
    }
    if (!string.IsNullOrEmpty(errorTasks.ToString()))
    {
        message += $"任务日志补上传失败:{errorTasks.ToString()}";
        isSuccess = false;
    }

    return new WorkResult(isSuccess, message);
}
    private async Task<WorkResult> StopTaskAsync()
    {
        List<string> stopTaskList = new List<string>();
        try
        {
            foreach (var kvRobotTask in _runningContext.RobotTasks)
            {
                string taskId = kvRobotTask.Key;
                var result = await _businessRequestService.GetTaskStatus(_robotConfig.HttpUrl, taskId);
                var res = JsonConvert.DeserializeObject<Result<string>>(result);
                if (res == null)
                {
                    continue;
                }

                if (res.Data == "5")
                {
                    stopTaskList.Add(taskId);
                    await _execManager.ServerStopExecutor(taskId);
                }
            }

            StringBuilder tasksBuilder = new StringBuilder();
            foreach (var task in stopTaskList)
            {
                tasksBuilder.Append(task);
                tasksBuilder.Append(";");
            }

            return new WorkResult(true, $"已停止任务:{tasksBuilder.ToString()}");
        }
        catch (Exception ex)
        {
            return new WorkResult(false, $"轮询停止任务时发生异常:{ex.ToString()}");
        }
    }
    private WorkResult ServerStateCheck()
    {
        try
        {
            if (string.IsNullOrEmpty(_robotConfig.HttpUrl))
            {
                IsServerAvailable = false;
                _logService.LogError("Server URL is not configured");
                return new WorkResult(false, "Server URL is not configured");
            }

            bool isServerAvailable = _businessRequestService.GetServerStateAsync(_robotConfig.HttpUrl).Result;

            if (IsServerAvailable != isServerAvailable)
            {
                IsServerAvailable = isServerAvailable;
                _logService.LogInfo($"Server state changed to: {(isServerAvailable ? "Available" : "Unavailable")}");
            }

            return new WorkResult(true);
        }
        catch (Exception ex)
        {
            IsServerAvailable = false;
            string errorMessage = $"Error monitoring server state: {ex.Message}";
            _logService.LogError(errorMessage);
            return new WorkResult(false, errorMessage);
        }
    }
    
    private WorkResult ReportState()
    {
        var result = _businessRequestService.WorkStateAsync(_robotConfig.HttpUrl, IsBusy ? 1 : 0).Result;
        if (string.IsNullOrEmpty(result))
        {
            return new WorkResult(true);
        }

        return new WorkResult(false, result);
    }
    
    private WorkResult GetOperation()
    {
        if (!IsRemote || !IsServerAvailable)
        {
            return new WorkResult(false, WorkerConst.NotConnectedMessage);
        }
        var getOperationResult = _businessRequestService.GetOperationTask(_robotConfig.HttpUrl).Result;
        if (getOperationResult.IsOk)
        {
            if (getOperationResult.Result.Equals("0"))
            {
                var res = RestartWithLauncher().Result;
                return res;
            }
            else if (string.IsNullOrEmpty(getOperationResult.Result))
            {
                return new WorkResult(true, "无需操作");
            }
            else
            {
                return new WorkResult(true, $"{getOperationResult.Result}");
            }
        }
        else
        {
            return new WorkResult(false, getOperationResult.Result);
        }
    }
    
    
    private WorkResult WorkStatusUpload()
    {
        var status = _workingTaskManager.GetAllStatus().Select(d => new WorkStatusModel()
        {
            Name = d.Name,
            DisplayName = d.DisplayName,
            Info = $"线程状态:{d.FriendlyStatus};间隔时间:{d.LoopSeconds}(秒);上一次执行的时间:{d.LastWorkTime};上一次执行结果:{(d.LastWorkResult.IsSuccess ? "成功" : "失败")};执行信息:{(string.IsNullOrEmpty(d.LastWorkResult.Reason) ? "无信息" : d.LastWorkResult.Reason)};重启次数:{d.RestartCount}"
        });
        var content = JsonConvert.SerializeObject(status);
        Console.WriteLine(content);
        var result = _businessRequestService.WorkMonitorUpload(_robotConfig.HttpUrl, content).Result;
        if (result.IsSuccess)
        {
            return new WorkResult(true);
        }
        else
        {
            return new WorkResult(false, result.Message);
        }
    }
    #endregion

    
    #region 机器人控制
    
    public async Task<WorkResult> RestartWithLauncher()
    {
        try
        {

            //TODO 添加重启小工具
            string launcherPath = "Skybridge.Robot.Launcher.exe"; // 启动器程序路径
            if (!File.Exists(launcherPath))
            {
                return new WorkResult(false, $"找不到重启的exe:{launcherPath}");
            }

            string mainAppPath = Process.GetCurrentProcess().MainModule.FileName; // 主程序路径
            int delay = 1000; // 延迟时间（毫秒）

            // 启动 Launcher
            Process.Start(new ProcessStartInfo
            {
                FileName = launcherPath,
                Arguments = $"\"{mainAppPath}\" {delay}",
                UseShellExecute = false,
                CreateNoWindow = true,
                WindowStyle = ProcessWindowStyle.Hidden
            });
            return new WorkResult(true);
        }
        catch (Exception ex)
        {
            var errorMessage = $@"重启执行器时发生错误:{ex.ToString()}";
            _logService.LogError(errorMessage);
            return new WorkResult(false, errorMessage);
        }
        finally
        {
            // 退出当前程序
            Environment.Exit(0);
        }
    }
    
    #endregion
    
    private async void OnBusyChangedHandler(bool isBusy)
    {
        await _businessRequestService.WorkStateAsync(_robotConfig.HttpUrl, isBusy ? 1 : 0);
    }

    private async Task RegisterEventHandlers()
    {
        _runningContext.TaskQueueEmpty += () =>
        {
            IsBusy = false;
        };
        _runningContext.TaskQueueNotEmpty += () =>
        {
            IsBusy = true;
        };
        // WebSocket事件处理
        _webSocketClient.OnMessageReceived += HandleWebSocketMessage;
        _webSocketClient.OnConnected += async (status) =>
        {
            _logService.LogInfo($"WebSocket connected: { status.Message}");
            await RegisterRobot();
        };

        
        if (_robotConfig.IsRemote)
        {
            await SetServerUrlAndRegister();
            try
            {
                await InitializeTaskMode();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }
    }


    private async void HandleWebSocketMessage(string message)
    {
        try
        {
            if (message.StartsWith("该消息不支持处理"))
            {
                return;
            }
            var msg = Message.Create(message);
            if (msg == null || msg.Value == null)
            {
                return;
            }
            switch (msg.MsType)
            {
                case MessageType.task:
                    ReceiveTaskBroadCast();
                    break;

                case MessageType.stop:
                    await StopTask(msg.Value.ToString());
                    break;

                case MessageType.license:
                    break;

                case MessageType.restart:
                    break;
                
            }
        }
        catch (Exception ex)
        {
            _logService.LogError( "Error processing WebSocket message",ex);
        }
    }

            public void ReceiveTaskBroadCast()
        {
            var pollingResult = PollingTask();
            if (!pollingResult.IsSuccess)
            {
                Console.WriteLine($@"Broadcast triggered polling task failed: {pollingResult.Reason}");
            }
        }

    //停止任务
    public async Task StopTask(string taskId)
    {
        await _execManager.ServerStopExecutor(taskId);
    }
    /// <summary>
    /// Starts the WebSocket reconnection loop.
    /// </summary>
    public void StartReconnectWebSocket()
    {
        // Prevent multiple concurrent reconnect tasks
        if (_webSocketReconnectTask != null && !_webSocketReconnectTask.IsCompleted)
            return;

        _webSocketReconnectCts = new CancellationTokenSource();
        _webSocketReconnectTask = Task.Run(
            () => ReconnectWebSocketLoop(_webSocketReconnectCts.Token));
    }

    /// <summary>
    /// Stops the WebSocket reconnection loop.
    /// </summary>
    public void StopReconnectWebSocket()
    {
        _webSocketReconnectCts?.Cancel();
        _webSocketReconnectTask = null;
    }

    /// <summary>
    /// The actual reconnect loop, runs in background.
    /// </summary>
    private async Task ReconnectWebSocketLoop(CancellationToken token)
    {
        while (!_webSocketClient.IsConnected && !token.IsCancellationRequested)
        {
            try
            {
                var wsUrl = AdaptWebSocketUrl(_robotConfig.HttpUrl);
                await _webSocketClient.ConnectAsync(wsUrl);
                await Task.Delay(5000, token); // Reconnect interval
            }
            catch (OperationCanceledException)
            {
                // Task was cancelled, exit loop
                break;
            }
            catch (Exception ex)
            {
                _logService.LogError( "WebSocket reconnection failed",ex);
                try
                {
                    await Task.Delay(5000, token); // Wait before retrying
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }
    }

    private async Task ReconnectWebSocket()
    {
        while (!_webSocketClient.IsConnected)
        {
            try
            {
                var wsUrl = AdaptWebSocketUrl(_robotConfig.HttpUrl);
                await _webSocketClient.ConnectAsync(wsUrl);
                await Task.Delay(5000); // 重连间隔
            }
            catch (Exception ex)
            {
                _logService.LogError( "WebSocket reconnection failed",ex);
                await Task.Delay(5000); // 重连失败后等待
            }
        }
    }

    private string AdaptWebSocketUrl(string httpUrl)
    {
        var uri = new Uri(httpUrl);
        var wsScheme = uri.Scheme == "https" ? "wss" : "ws";
        return $"{wsScheme}://{uri.Authority}/ws/robot/{_robotConfig.RobotId}";
    }

    private async Task InitializeTaskMode()
    {

        await InitializeWebsocketMode();
        StartReconnectWebSocket();

        await ChangeState(1);

        await ChangeState(IsBusy ? 1 : 0);
    }

    public void StopPollingAndReconnect()
    {
        try
        {
            StopReconnectWebSocket();
            _pollingCancellationTokenSource?.Cancel();
            _pollingCancellationTokenSource?.Dispose();
            _pollingCancellationTokenSource = null;
            if (_webSocketClient.IsConnected)
            {
                _webSocketClient.CloseAsync().Wait();
            }

        }
        catch (Exception ex)
        {
            _logService.LogError("Error stopping current mode",ex);
        }
    }

    public async Task InitializeWebsocketMode()
    {
        try
        {
            var serverUrl = _robotConfig.HttpUrl;

            _logService.LogInfo($"Initializing WebSocket mode with server URL: {serverUrl}");
            // close the client before a new connection create
            await _webSocketClient.CloseAsync();
            // WebSocket client will handle URL normalization internally
            await _webSocketClient.ConnectAsync(serverUrl);
            _logService.LogInfo("WebSocket mode initialized successfully");
        }
        catch (Exception ex)
        {
            _logService.LogError( "Failed to initialize WebSocket mode",ex);
            throw;
        }
    }


    public async Task<bool> RegisterRobot()
    {
        try
        {
            await _businessRequestService.RegisterRobotAsync(
                _robotConfig.HttpUrl,
                new RobotRegisterRequest
                {
                    Host = _environmentConfig.IpAddress,
                    Name = _robotConfig.RobotName,
                    Port = 1000,
                    Robot_ID = _robotConfig.RobotId,
                    System = _environmentConfig.OSPlatformName,
                    Memory = _environmentConfig.MemorySize,
                    Cpu = _environmentConfig.CpuInfo,
                    ExpiredDate = _robotConfig.ExpiredDate.ToString("yyyy-MM-dd")
                });
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 更改机器人状态
    /// state: 0:空闲 1:忙碌 2:离线 3:失联
    /// </summary>
    public async Task ChangeState(int state)
    {
        try
        {
            await _businessRequestService.WorkStateAsync(_robotConfig.HttpUrl, state);
            _logService.LogInfo($"机器人状态更改为: {state}");
        }
        catch (Exception e)
        {
            _logService.LogError("机器人状态更改失败",e);
        }
    }

    private async Task<bool> SetServerUrlAndRegister()
    {
        try
        {
            if (await RegisterRobot())
            {
                _logService.LogInfo("服务器连接成功");
                return true;
            }
            else
            {
                _logService.LogError("服务器连接失败");
                return false;
            }
        }
        catch (Exception e)
        {
            _logService.LogError("服务器连接失败，请检查服务器地址",e);
            return false;
        }
    }


    
    private bool IsServerTask(string id)
    {
        return int.TryParse(id, out int _);
    }
    public int GetStatus(TaskLog taskLog)
    {
        if (taskLog.Status == "Running")
        {
            return 0;
        }

        if (taskLog.Status == "Fail")
        {
            return 2;
        }

        return 1;
    }
    #region setActivityLog

    private ActivityLogModel GetActivityLogModel(TaskLog taskLog)
    {
        ActivityLogModel activityLogModel = new ActivityLogModel();
        activityLogModel.ActivityName = GetActivityName(taskLog.traceInfo.Message);
        activityLogModel.TaskDetail = GetActivityState(taskLog.traceInfo.Message);
        DateTime? startTime = GetStartTime(taskLog.traceInfo.Message);
        long milliseconds = 0;
        if (startTime != null)
        {
            milliseconds = (long)(startTime.Value.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
        }
        activityLogModel.CreateTime = milliseconds.ToString();
        activityLogModel.TaskId = taskLog.TaskId;
        activityLogModel.ProjectName = taskLog.TaskId;
        activityLogModel.LogType = GetLogType(taskLog.traceInfo.Message);
        return activityLogModel;
    }

    private string GetActivityName(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }
        string componentName = Regex.Match(message, @"名称：\s+(\S+)").Groups[1].Value;
        return componentName;
    }

    private string GetLogType(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }
        string state = Regex.Match(message, @"状态：(\S+)").Groups[1].Value;

        if (state.Equals("Executing"))
        {
            return "INFO";
        }
        else if (state.Equals("Closed"))
        {
            return "INFO";
        }
        else if (state.Equals("Faulted"))
        {
            return "ERROR";
        }
        else
        {
            return "INFO";
        }
    }

    private string GetActivityState(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return message;
        }
        string state = Regex.Match(message, @"状态：(\S+)").Groups[1].Value;

        if (state.Equals("Executing"))
        {
            return "开始";
        }
        else if (state.Equals("Closed"))
        {
            return "结束";
        }
        else if (state.Equals("Faulted"))
        {
            return "失败";
        }
        else
        {
            return message;
        }
    }

    private DateTime? GetStartTime(string message)
    {
        if (string.IsNullOrEmpty(message))
        {
            return null;
        }
        string time = Regex.Match(message, @"跟踪时间：(\S+)").Groups[1].Value;
        if (!string.IsNullOrEmpty(time))
        {
            return Convert.ToDateTime(time);
        }
        else
        {
            return null;
        }
    }

    private void UploadActivityLogTask(ActivityLogModel activityLogModel)
    {
        Task.Run(async () =>
        {
            string activityLogContent = JsonConvert.SerializeObject(activityLogModel);
            await _businessRequestService.UploadActivityLog(_robotConfig.HttpUrl, activityLogContent);
        });
    }

    #endregion setActivityLog

    private static int GetLogStatus(string status) => status switch
    {
        "Running" => 0,
        "Fail" => 2,
        _ => 1
    };
    private async Task<(bool IsSuccess, string ErrorMessage)> DownLoadProject(string baseUrl, RobotProject robotProject)
    {
        if (Directory.Exists(_fileStorageConfig.TempPath))
        {
            Directory.Delete(_fileStorageConfig.TempPath,true);
        }
        Directory.CreateDirectory(_fileStorageConfig.TempPath);
        var zipPath = Path.Combine(_fileStorageConfig.TempPath, 
            $"{GetProjectDirName(true, robotProject.Name, robotProject.Code, robotProject.Version)}.zip");
        var downloadRes = await _businessRequestService.DownloadFileAsync(baseUrl, robotProject.ProjectContentId, zipPath);
        if (!downloadRes.IsSuccess)
        {
            return (false, $"下载项目文件失败:{downloadRes.ErrorMessage}");
        }
        var importRes = RemoteImport(zipPath, robotProject);
        if (!importRes.IsSuccess)
        {
            _logService.LogError($"导入项目出现错误:{importRes.ErrorMessage}");
        }
        return importRes;
    }


    public void StopServerStateMonitorTask()
    {
        _logService.LogInfo("停止执行器心跳监测");
        _monitorCts.Cancel();
    }
    
        public async Task<WorkResult> RunTask(TaskInfo taskInfo)
    {
        RobotTask receivedTask = null;
        try
        {
            var uploadTask = new UploadTask(taskInfo.TaskId, taskInfo.ProjectName, taskInfo.Tags);
            _runningContext.AddUploadTask(uploadTask);
            var logUploadResult = new TaskLogUploadResult();
            for (int i = 0; i < 10; i++)
            {
                logUploadResult = await _robotLogService.UploadTaskRunningLog(taskInfo.TaskId, "开始执行");
                if (logUploadResult.IsSuccess)
                {
                    break;
                }

                await System.Threading.Tasks.Task.Delay(10 * 1000);
            }

            if (!logUploadResult.IsSuccess)
            {
                string errorMessage = $"上报执行中失败,任务日志接口调用异常:{logUploadResult.Message}";
                _logService.LogError(errorMessage);
                _runningContext.RemoveUploadTask(uploadTask.Id);
                return new WorkResult(false, errorMessage);
            }

            _remoteLog.AddLog(taskInfo.TaskId, $"接收任务", OperationResult.Success, OperationType.Start);
            RobotProject proj = null;
            try
            {
                proj = _runningContext.RobotProjects.Find(x =>
x.Name.Equals(taskInfo.ProjectName) && x.Version.Equals(taskInfo.Version) && x.Code.Equals(taskInfo.Code) && x.IsRemote);
            }
            catch (Exception ex)
            {
                proj = null;
            }

            if (proj == null)
            {
                proj = GetProject(taskInfo);
            }
            var taskId = taskInfo.TaskId;
            var addTaskResult = _runningContext.CanAddTask(proj.Id, taskId);
            if (!addTaskResult.IsSuccess)
            {
                _remoteLog.AddLog(taskInfo.TaskId, $"任务被拒绝: {addTaskResult.Reason}", OperationResult.Fail, OperationType.End);
                _logService.LogInfo($"任务被拒绝: {addTaskResult.Reason}");
                await RejectedLog(taskInfo, addTaskResult.Reason);
                return addTaskResult;
            }

            if (!taskInfo.IsSingleTask())
            {
                taskInfo.IsVideoLog = false;
            }

            receivedTask = AddTask(proj, taskId, taskInfo.IsVideoLog, taskInfo.Parameters, taskInfo.Token, taskInfo.ParentId);
            // Use the concurrent dictionary to ensure single download per project
            string projectKey = GetProjectKey(taskInfo);
            taskInfo.ProjectRoot = proj.FilePath;
            var downloadTask = _activeDownloads.GetOrAdd(projectKey,
                key => DownloadAndExtractProjectAsync(taskInfo, proj));

            bool downloadSuccess = await downloadTask;

            _activeDownloads.TryRemove(projectKey, out _);

            if (!downloadSuccess)
            {
                string reason = "下载项目过程出错,项目下载异常,详情查看操作日志";
                StopWithError(receivedTask, reason);
                return new WorkResult(false, reason);
            }

            string packageJsonPath = Path.Combine(taskInfo.ProjectRoot, JsonConst.JsonPackages);

            if (File.Exists(packageJsonPath))
            {
                var getMergedRes = PackagesDownloadCore.Instance.GetMergedPacakges(taskInfo.ProjectRoot, GetBusinessFolder(taskInfo.ProjectRoot));
                if (!string.IsNullOrEmpty(getMergedRes.Item1))
                {
                    var errorMessage = $"组件包json读取出错:{getMergedRes.Item1}";
                    StopWithError(receivedTask, errorMessage);
                    _logService.LogError(errorMessage);
                    _remoteLog.AddLog(taskInfo.TaskId, errorMessage, OperationResult.Fail, OperationType.End);
                    return new WorkResult(false, errorMessage);
                }
                var loadRes = await LoadActivityAfterRemoteImport(taskInfo, getMergedRes.Item2);
                if (!loadRes.IsSuccess)
                {
                    var errorMessage = $"导入后加载组件包出错:{loadRes.ErrorMessage}";
                    // StopWithError(receivedTask, errorMessage);
                    _logService.LogError(errorMessage);
                    _remoteLog.AddLog(taskInfo.TaskId, errorMessage, OperationResult.Fail, OperationType.End);
                    // return new WorkResult(false, errorMessage);
                    proj.IsLoadedPackages = false;
                }
            }
            else
            {
                proj.IsLoadedPackages = false;
            }

            if (!proj.IsLoadedPackages)
            {
                if (!Directory.Exists(_fileStorageConfig.PackagesFolderPath))
                {
                    string reason = $"没有packages包,{_fileStorageConfig.PackagesFolderPath}";
                    StopWithError(receivedTask, reason);
                    return new WorkResult(false, reason);
                }
            }
            

            Dictionary<string, object> inParameters = new Dictionary<string, object>();
            if (taskInfo.IsSingleTask())
            {
                if (taskInfo.Parameters != null)
                {
                    inParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(taskInfo.Parameters);
                }
            }
            _remoteLog.AddLog(taskInfo.TaskId, $"执行任务", OperationResult.Success, OperationType.Start);
            AddToRunningContext(proj);
            await Execute(taskId, proj, taskInfo, inParameters);
            return new WorkResult(true);
        }
        catch (Exception e)
        {
            string reason;
            if (receivedTask != null)
            {
                reason = $"发生异常:{e.Message + ":" + e.ToString()}";
                StopWithError(receivedTask, reason);
            }

            reason = $"接收任务过程出错:{e.ToString()}";
            _logService.LogError(reason);
            _remoteLog.AddLog(taskInfo.TaskId, reason, OperationResult.Fail, OperationType.End);
            await RejectedLog(taskInfo, reason);
            return new WorkResult(false, reason);
        }
    }
        public async Task<WorkResult> LocalRunTask(TaskInfo taskInfo)
        {
            RobotProject proj = null;

            try
            {
                proj = _runningContext.RobotProjects.Find(x =>
                    x.Id.Equals(taskInfo.ProjectId) && x.Version.Equals(taskInfo.Version));
            }
            catch (Exception ex)
            {
                proj = null;
            }

            if (proj == null)
            {
                proj = GetProject(taskInfo);
            }
            if (!taskInfo.IsSingleTask())
            {
                taskInfo.IsVideoLog = false;
            }
            AddTask(proj, taskInfo.TaskId, taskInfo.IsVideoLog, taskInfo.Parameters, taskInfo.Token, taskInfo.ParentId);
            string projectKey = GetProjectKey(taskInfo);
            taskInfo.ProjectRoot = proj.FilePath;
            if (_commonConfig.EnableDownload && 
                !string.IsNullOrEmpty(taskInfo.Code) && 
                !string.IsNullOrEmpty(_robotConfig.HttpUrl))
            {
                var res = 
                    await _businessRequestService.GetActiveProjectInfoByCode
                        (_robotConfig.HttpUrl,taskInfo.Code);
                if (res.isSuccess)
                {
                    if (res.activeProjectInfo.Version != taskInfo.Version)
                    {
                        taskInfo.Version = res.activeProjectInfo.Version;
                        string projectDirName = GetProjectDirName(true, taskInfo.ProjectName, 
                            taskInfo.Code, taskInfo.Version);
                        string projectPath = Path.Combine(_fileStorageConfig.ProjectPath, projectDirName);
                        //TODO 下载激活的版本
                        taskInfo.Version = res.activeProjectInfo.Version;
                        taskInfo.ProjectRoot = projectPath;
                        proj.FilePath = projectPath;
                        proj.Version = res.activeProjectInfo.Version;
                    }
                }
                var downloadTask = _activeDownloads.GetOrAdd(projectKey,
                    key => DownloadAndExtractProjectAsync(taskInfo, proj));

                await downloadTask;

                _activeDownloads.TryRemove(projectKey, out _);
                string packageJsonPath = Path.Combine(taskInfo.ProjectRoot, JsonConst.JsonPackages);

                if (File.Exists(packageJsonPath))
                {
                    var getMergedRes = PackagesDownloadCore.Instance.GetMergedPacakges(taskInfo.ProjectRoot, GetBusinessFolder(taskInfo.ProjectRoot));
                    if (string.IsNullOrEmpty(getMergedRes.Item1))
                    {
                        await LoadActivityAfterRemoteImport(taskInfo, getMergedRes.Item2);
                    }
                }
                else
                {
                    proj.IsLoadedPackages = false;
                }

                if (!proj.IsLoadedPackages)
                {
                    if (!Directory.Exists(_fileStorageConfig.PackagesFolderPath))
                    {
                        string reason = $"没有packages包,{_fileStorageConfig.PackagesFolderPath}";
                        return new WorkResult(false, reason);
                    }
                }
            }

            Dictionary<string, object> inParameters = new Dictionary<string, object>();
            if (taskInfo.IsSingleTask())
            {
                if (taskInfo.Parameters != null)
                {
                    inParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(taskInfo.Parameters);
                }
            }
            AddToRunningContext(proj);
            await Execute(taskInfo.TaskId, proj, taskInfo, inParameters);
            return new WorkResult(true);
        }
        
        public async Task Execute(string taskId, RobotProject robotProject, TaskInfo taskInfo, IDictionary<string, object> inParameters)
        {
            ExecuteTaskInfo executeInfo = GenerateTaskInfo(
                _robotWebServer.ListenPort, taskId, taskInfo.TaskId, 
                robotProject, inParameters, taskInfo.LogType, 
                taskInfo.Token, taskInfo.ProjectRoot);
            string base64 = Helpers.StringToBase64(JsonConvert.SerializeObject(executeInfo));
            await _execManager.StartExecutorAsync(new StartOption()
            {
                TaskId = taskId,
                Base64Str = base64,
                InParameters = inParameters,
                IsSingleTask = taskInfo.IsSingleTask()
            });
        }
        private ExecuteTaskInfo GenerateTaskInfo(int port, string taskId, string serverTaskId, RobotProject project,
            IDictionary<string, object> inParameters, string logType, string token, string projectDirRoot)
        {
            var projectActivityFolder = GetProjectActivityFolder(project);
            string isParameters;
            if (inParameters == null || inParameters.Count == 0)
            {
                isParameters = "0";
            }
            else
            {
                isParameters = "1";
            }

            string httpUrl = "";
            if (IsRemote)
            {
                httpUrl = _robotConfig.HttpUrl;
            }
            return new ExecuteTaskInfo()
            {
                ActivityRoot = projectActivityFolder,
                ProjectCode = project.Code,
                ProjectId = project.ProjectContentId,
                ProjectName = project.Name,
                ProjectRoot = projectDirRoot,
                ProjectVersion = project.Version,
                IsParameters = isParameters,
                RobotId = _robotConfig.RobotId,

                ActivityParameter = new ActivityParameter()
                {
                    RobotExecuteContext = new RobotExecuteContext()
                    {
                        DateTokenId = token,
                        FlowId = "",
                        SceneId = "",
                        ServiceId = serverTaskId,
                        TaskId = taskId,
                        LogLevel = logType
                    },
                    ServerInfoContext = new ServerInfoContext()
                    {
                        Gateway = "/api",
                        ServerEndPoint = httpUrl,
                        ServerRange = ""
                    }
                },
                StartupMain = "Main.xaml",
                TaskId = taskId,
                TaskName = project.Name,
                ServerUrl = $"http://{IPAddress.Loopback}:{port}",
            };
        }
        public void CopyAll(DirectoryInfo source, DirectoryInfo target)
        {
            if (!Directory.Exists(target.FullName)) Directory.CreateDirectory(target.FullName);
            foreach (var fi in source.GetFiles()) fi.CopyTo(Path.Combine(target.FullName, fi.Name), true);
            foreach (var diSourceSubDir in source.GetDirectories())
            {
                var nextTargetSubDir = target.CreateSubdirectory(diSourceSubDir.Name);
                CopyAll(diSourceSubDir, nextTargetSubDir);
            }
        }

        private string GetProjectActivityFolder(RobotProject project) 
        {
            //如果有code表示是远程项目下载的或者拉取的，如果没有code则表示是
            string projectActivityFolder;
            if (string.IsNullOrEmpty(project.Code))
            {
                projectActivityFolder = Path.Combine(_fileStorageConfig.ProjectActivityPath,
                    "L" + "_" + project.Name + "_" + project.Version);
                string projectPackages = Path.Combine(project.FilePath, "packages");
                if (!Directory.Exists(projectActivityFolder))
                {
                    if (!Directory.Exists(projectPackages))
                    {
                        projectActivityFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "packages");
                    }
                    else
                    {
                        CopyAll(new DirectoryInfo(projectPackages), new DirectoryInfo(projectActivityFolder));
                    }
                }
            }
            else
            {
                if (!project.IsLoadedPackages)
                {
                    projectActivityFolder = _fileStorageConfig.PackagesFolderPath;
                }
                else
                {
                    projectActivityFolder = Path.Combine(_fileStorageConfig.ProjectActivityPath, project.Code + "_" + project.Version);
                }
            }

            return projectActivityFolder;
        }
        private string GetBusinessFolder(string projectFolder)
        {
            return Path.Combine(projectFolder, ".projectdependence", ".business");
        }
        private async Task RejectedLog(TaskInfo taskInfo, string rejectReason)
        {
            TaskLog taskLog = new TaskLog()
            {
                TaskId = taskInfo.TaskId,
                Message = rejectReason,
                Status = "Fail",
                traceInfo = null
            };
            await _robotLogService.UploadRejectionLog(taskLog);
        }
        private void StopWithError(RobotTask robotTask, string reason)
        {
            robotTask.StopType = 2;
            robotTask.StopMessage = reason;

            _runningContext.OnTaskCompleted?.Invoke(robotTask);
        }

    public void AddToRunningContext(RobotProject proj)
    {
        RobotProject? robotProject = null;
        robotProject = _runningContext.RobotProjects.Find(d => d.Id.Equals(proj.Id));
        if (robotProject == null)
        {
            _runningContext.RobotProjects.Add(proj);
            _runningContext.OnProjectAdded?.Invoke(proj);
        }
        else
        {
            if (!robotProject.Versions.Exists(d => d.Equals(proj.Version)))
            {
                robotProject.FilePaths.Add(proj.FilePath);
                robotProject.FilePath = proj.FilePath;
                robotProject.Versions.Add(proj.Version);
                robotProject.Version = proj.Version;
                _runningContext.OnProjectUpdated(robotProject);
            }
        }
    }
    private string GetProjectKey(TaskInfo taskInfo)
    {
        return $"{taskInfo.ProjectName}_{taskInfo.Version}_{taskInfo.Code}";
    }
    public RobotTask AddTask(RobotProject project,string taskId,bool isVideo, string parameters, string token = "", string parentId = "")
    {
        var task = new RobotTask(taskId,
            project.Id,
            project.Name,
            project.Version,
            DateTime.Now,
            0,
            isVideo,
            project.Category,
            parameters,
            parentId,
            token
        );
        _logService.LogInfo($"添加任务 {taskId} 到执行器");
        _runningContext.RobotTasks.TryAdd(taskId, task);
        _runningContext.OnTaskAdded?.Invoke(task);

        return task;
    }
    
    private async Task<bool> DownloadAndExtractProjectAsync(TaskInfo taskInfo, RobotProject proj)
    {
        try
        {
            if (!File.Exists(proj.ZipFilePath))
            {
                string projectRoot = Path.GetDirectoryName(proj.FilePath);
                if (!Directory.Exists(projectRoot))
                {
                    Directory.CreateDirectory(projectRoot);
                }
                _remoteLog.AddLog(taskInfo.TaskId, $"项目下载", OperationResult.Success, OperationType.Start);
                var downloadRes =  await DownLoadProject(_robotConfig.HttpUrl,proj);
                if (!downloadRes.IsSuccess)
                {
                    _remoteLog.AddLog(taskInfo.TaskId, $"项目下载：{downloadRes}", OperationResult.Fail, OperationType.End);
                    return false;
                }
                _remoteLog.AddLog(taskInfo.TaskId, $"项目下载", OperationResult.Success, OperationType.End);
            }

            if (!Directory.Exists(proj.FilePath))
            {
                Directory.CreateDirectory(proj.FilePath);
                bool isExtracted = FileHelper.ExtractZipFile(proj.ZipFilePath, taskInfo.ProjectRoot, out string errorMessage);
                if (!isExtracted)
                {
                    _remoteLog.AddLog(taskInfo.TaskId, $"解压出现问题: {errorMessage}", OperationResult.Success, OperationType.End);
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            _remoteLog.AddLog(taskInfo.TaskId, $"下载和解压过程出错: {ex.Message}", OperationResult.Fail, OperationType.End);
            return false;
        }
    }
        public (bool IsSuccess, string ErrorMessage) RemoteImport(string zipPath, RobotProject robotProject)
    {
        string tempPath = _fileStorageConfig.TempPath;

        #region 检查是否包含project.json并复制到用户目录

        bool isUnzipped = false;
        try
        {
            using var archive = ZipFile.OpenRead(zipPath);

            if (archive.Entries.Any(entry => entry.FullName.Contains("project.json")))
            {
                isUnzipped = FileHelper.ExtractZipFile(zipPath, tempPath, out string errorMessage);
            }
            else
            {
                return (false, $"该项目包不存在project.json文件");
            }
        }
        catch (Exception ex)
        {
            return (false, $"下载项目文件失败: {ex.ToString()}");
        }

        #endregion 检查是否包含project.json并复制到用户目录

        #region 读取并反序列化 project.json

        // 读取并反序列化 project.json
        var directories = Directory.GetDirectories(tempPath);
        var files = Directory.GetFiles(tempPath);
        if (directories.Count() == 1 && files.Count() == 0)
        {
            FileHelper.CopyAll(new DirectoryInfo(directories[0]), new DirectoryInfo(tempPath));
            Directory.Delete(directories[0], true);
        }
        var newDirectories = Directory.GetDirectories(tempPath);
        foreach (var item in newDirectories)
        {
            string name = Path.GetFileName(item); // 注意：使用 GetFileName 获取文件夹名
            if (name.Equals(".packages")) // 检查是否是目标文件夹（可能需要修正拼写）
            {
                string parentPath = Path.GetDirectoryName(item);
                string newPath = Path.Combine(parentPath, "packages"); // 指定新的文件夹名

                try
                {
                    Directory.Move(item, newPath); // 执行重命名操作
                    Console.WriteLine($"已将文件夹 {item} 重命名为 {newPath}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"重命名文件夹时出错: {ex.Message}");
                }
            }
        }
        // 读取并反序列化 project.json
        string projectJsonPath = Path.Combine(tempPath, "project.json");
        if (!File.Exists(projectJsonPath))
            return (false, "缺失项目文件: project.json");

        ProjectModel project;
        try
        {
            string projectJson = File.ReadAllText(projectJsonPath);
            project = JsonConvert.DeserializeObject<ProjectModel>(projectJson);
        }
        catch (Exception ex)
        {
            return (false, $"解析项目文件失败: {ex.Message}");
        }

        #endregion 读取并反序列化 project.json

        #region 复制文件

        // 复制文件
        try
        {
            File.Delete(zipPath);
            FileHelper.CreateZip(robotProject.ZipFilePath, tempPath);
        }
        catch (Exception ex)
        {
            return (false, $"文件复制失败: {ex.Message}");
        }
        finally
        {
            // 删除临时文件
            Directory.Delete(tempPath, true);
        }

        #endregion 复制文件

        return (true, "导入成功");
    }
    public string GetProjectDirName(bool isRemote, string name, string code, string version)
    {
        string res = string.Format("{0}-{1}-{2}-{3}", isRemote ? "R" : "L", name, code, version);
        return res;
    }
    private RobotProject GetProject(TaskInfo taskInfo)
    {
        string projectDirName = GetProjectDirName(true, taskInfo.ProjectName, taskInfo.Code, taskInfo.Version);
        string projectPath = Path.Combine(_fileStorageConfig.ProjectPath, projectDirName);
        var robotProject = new RobotProject(
            id: taskInfo.ProjectId,
            projectContentId: taskInfo.ProjectContentId,
            isRemote: true,
            code: taskInfo.Code,
            name: taskInfo.ProjectName,
            version: taskInfo.Version,
            filePath: projectPath,
            description: taskInfo.Description,
            tags: taskInfo.Label == null ? new() : taskInfo.Label.Split('/').ToList(),
            category: taskInfo.Category,
            parentId: taskInfo.ParentId,
            fileHash: taskInfo.FileHash,
            fileSize: taskInfo.FileSize
        );
        robotProject.FilePaths.Add(robotProject.FilePath);
        robotProject.Versions.Add(robotProject.Version);
        return robotProject;
    }
        /// <summary>
        /// 导入项目文件，返回元组：是否成功、错误信息、项目实例。
        /// </summary>
        /// <param name="zipPath">压缩文件路径。</param>
        /// <param name="isRemote">是否远程项目。</param>
        /// <param name="alias">项目别名。</param>
        /// <param name="version">项目版本。</param>
        /// <returns>元组：是否成功、错误信息、项目实例。</returns>
        public (bool IsSuccess, string ErrorMessage, RobotProject Project, string projectFolder) 
            LocalImport(string zipPath)
        {
            string tempPath = _fileStorageConfig.TempPath;
            if (Directory.Exists(tempPath))
            {
                Directory.Delete(tempPath, true);
            }
            Directory.CreateDirectory(tempPath);

            #region 解压并检查是否包含project.json

            // 解压并检查是否包含 project.json
            bool isUnzipped = false;
            try
            {
                using var archive = ZipFile.OpenRead(zipPath);
                if (archive.Entries.Any(entry => entry.FullName.Contains("project.json")))
                {
                    ZipFile.ExtractToDirectory(zipPath, 
                        tempPath, Encoding.GetEncoding("GB2312"));
                    isUnzipped = true;
                }
            }
            catch (Exception ex)
            {
                return (false, 
                    $"解压失败: {ex.Message}", null, string.Empty);
            }

            if (!isUnzipped)
                return (false, 
                    "无法读取到项目文件，导入失败", null, string.Empty);

            #endregion 解压并检查是否包含project.json

            #region 读取并反序列化 project.json

            // 读取并反序列化 project.json
            var directories = Directory.GetDirectories(tempPath);
            var files = Directory.GetFiles(tempPath);
            if (directories.Count() == 1 && files.Count() == 0)
            {
                CopyAll(new DirectoryInfo(directories[0]), new DirectoryInfo(tempPath));
                Directory.Delete(directories[0], true);
            }
            var newDirectories = Directory.GetDirectories(tempPath);
            foreach (var item in newDirectories)
            {
                string name = Path.GetFileName(item); // 注意：使用 GetFileName 获取文件夹名
                if (name.Equals(".packages")) // 检查是否是目标文件夹（可能需要修正拼写）
                {
                    string parentPath = Path.GetDirectoryName(item);
                    string newPath = Path.Combine(parentPath, "packages"); // 指定新的文件夹名

                    try
                    {
                        Directory.Move(item, newPath); // 执行重命名操作
                        Console.WriteLine($"已将文件夹 {item} 重命名为 {newPath}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"重命名文件夹时出错: {ex.Message}");
                    }
                }
            }
            string projectJsonPath = Path.Combine(tempPath, "project.json");
            if (!File.Exists(projectJsonPath))
                return (false, "缺失项目文件: project.json", null, string.Empty);

            ProjectModel project;
            try
            {
                string projectJson = File.ReadAllText(projectJsonPath);
                project = JsonConvert.DeserializeObject<ProjectModel>(projectJson);
            }
            catch (Exception ex)
            {
                return (false, $"解析项目文件失败: {ex.Message}", null, string.Empty);
            }

            #endregion 读取并反序列化 project.json

            #region 构建项目路径和实例

            // 构建项目路径和实例
            string projectName = project.Name;
            if (project.PublishLocalPackages == null)
            {
                project.PublishLocalPackages = new ProjectPublishLocal();
                project.PublishLocalPackages.PublishVersion = "1.0.0";
            }
            string projectVersion = project.PublishLocalPackages.PublishVersion;
            string projectPath = Path.Combine(
                _fileStorageConfig.ProjectPath,
                GetProjectDirName(false, projectName, project.Code, projectVersion)
            );

            var proj = new RobotProject(
                id: project.Id,
                projectContentId: string.Empty,
                isRemote: false,
                code: project.Code,
                name: projectName,
                version: projectVersion,
                filePath: projectPath,
                description: project.Description,
                tags: project.Tags,
                category: project.Category,
                parentId: string.Empty,
                fileHash: string.Empty
            );
            proj.FilePaths.Add(proj.FilePath);
            proj.Versions.Add(proj.Version);

            #endregion 构建项目路径和实例

            #region 复制文件

            // 复制文件
            try
            {
                if (!Directory.Exists(projectPath))
                {
                    CopyAll(new DirectoryInfo(tempPath), new DirectoryInfo(projectPath));
                }
            }
            catch (Exception ex)
            {
                return (false, $"文件复制失败: {ex.Message}", null, string.Empty);
            }
            finally
            {
                // 删除临时文件
                Directory.Delete(tempPath, true);
            }

            #endregion 复制文件

            #region 更新运行上下文

            AddToRunningContext(proj);

            #endregion 更新运行上下文

            return (true, "导入成功", proj, projectPath);
        }

    
    #region 组件包相关的操作
    /// <summary>
    /// 用于判断项目组件文件夹中是否存在对应的name_projectactivity文件夹
    /// </summary>
    /// <param name="robotProject"></param>
    /// <returns></returns>
    public (bool IsSuccess, string projectActivitySubFolder) IsProjectActivityExist(string code, string version)
    {
        string folderName = Path.Combine(_fileStorageConfig.ProjectActivityPath, code + "_" + version);

        #region 外部目录不存在直接返回false

        if (!Directory.Exists(_fileStorageConfig.ProjectActivityPath))
        {
            return new(false, folderName);
        }

        #endregion 外部目录不存在直接返回false

        if (Directory.Exists(folderName))
        {
            if (Directory.GetFiles(folderName).Any())
            {
                return new(true, folderName);
            }
            else
            {
                return new(false, folderName);
            }
        }
        else
        {
            return new(false, folderName);
        }
    }
            public async Task<(bool IsSuccess, string ErrorMessage)> LoadActivityAfterRemoteImport(TaskInfo taskInfo, List<DependencyPackage> packages)
        {
            _remoteLog.AddLog(taskInfo.TaskId, $"生成项目组件文件夹", OperationResult.Success, OperationType.Start);

            var existRes = IsProjectActivityExist(taskInfo.Code, taskInfo.Version);
            if (existRes.IsSuccess)
            {
                _remoteLog.AddLog(taskInfo.TaskId, $"项目组件文件夹已存在", OperationResult.Success, OperationType.End);
                return new(true, string.Empty);
            }

            string projectActivitySubFolderPath = existRes.projectActivitySubFolder;
            string packageKey = GetPackageKey(taskInfo.Code, taskInfo.Version);

            // Get or add the download task using the concurrent dictionary
            var downloadTask = _activePackageDownloads.GetOrAdd(packageKey, async key =>
            {
                if (!Directory.Exists(_fileStorageConfig.ActivitiesPackagesPath))
                {
                    Directory.CreateDirectory(_fileStorageConfig.ActivitiesPackagesPath);
                }

                var downloadRes = await
                    DownloadActivityPackages(packages, _fileStorageConfig.ActivitiesPackagesPath, taskInfo);
                if (!downloadRes.IsSuccess)
                {
                    if (Directory.Exists(projectActivitySubFolderPath))
                    {
                        Directory.Delete(projectActivitySubFolderPath, true);
                    }
                    _logService.LogError(downloadRes.ErrorMessage);
                    return false;
                }

                if (!Directory.Exists(projectActivitySubFolderPath))
                {
                    Directory.CreateDirectory(projectActivitySubFolderPath);
                }

                return ExtractPackagesAsync(taskInfo, projectActivitySubFolderPath, packages);
            });

            bool success = await downloadTask;

            // Cleanup completed download task
            _activePackageDownloads.TryRemove(packageKey, out _);

            if (!success)
            {
                return new(false, "下载或解压组件包失败");
            }

            _remoteLog.AddLog(taskInfo.TaskId, $"生成项目组件文件夹", OperationResult.Success, OperationType.End);
            return new(true, string.Empty);
        }
            
        private bool ExtractPackagesAsync(TaskInfo taskInfo, string projectActivitySubFolderPath, List<DependencyPackage> dependencyPackages)
        {
            try
            {
                foreach (var package in dependencyPackages)
                {
                    string zipFile = Path.Combine(_fileStorageConfig.ActivitiesPackagesPath, package.Name + "_" + package.Version + ".zip");
                    if (File.Exists(zipFile))
                    {
                        bool isExtractSuccess = FileHelper.ExtractZipFile(zipFile, projectActivitySubFolderPath, out string errorMessage);
                        if (!isExtractSuccess)
                        {
                            _logService.LogError($"解压组件包发生异常:{zipFile}");
                            _remoteLog.AddLog(taskInfo.TaskId, $"解压组件包发生异常：{zipFile}", OperationResult.Fail, OperationType.End);
                            return false;
                        }
                    }
                    else
                    {
                        _logService.LogError($"不存在依赖组件包:{zipFile}");
                        _remoteLog.AddLog(taskInfo.TaskId, $"不存在依赖组件包：{zipFile}", OperationResult.Fail, OperationType.End);
                        return false;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _remoteLog.AddLog(taskInfo.TaskId, $"下载和解压组件包出错: {ex.Message}", OperationResult.Fail, OperationType.End);
                return false;
            }
        }
        private string GetPackageKey(string code, string version)
        {
            return $"{code}_{version}_package";
        }
        
        private async Task<(bool IsSuccess, string ErrorMessage)> DownloadActivityPackages(List<DependencyPackage> dependencyPackages, string activityPackageFolder, TaskInfo taskInfo)
        {
            if (dependencyPackages == null || dependencyPackages.Count == 0)
            {
                return new(true, string.Empty);
            }
            string url = _robotConfig.HttpUrl;
            string[] currentPackages = Directory.GetFiles(activityPackageFolder, "*.zip");
            foreach (var package in dependencyPackages)
            {
                string packageName = package.Name;
                string packageVersion = package.Version;
                string destFile = Path.Combine(activityPackageFolder, packageName + "_" + package.Version + ".zip");
                _remoteLog.AddLog(taskInfo.TaskId, $"下载组件包：{packageName}_{packageVersion}", OperationResult.Success, OperationType.Start);
                if (!currentPackages.Any(d => d.Contains(destFile
                    )))
                {
                //TODO 需要根据当前的cpu架构去获取项目组件包或组件
                string system = RuntimeInfoHelper.GetRuntimeIdentifier();
                var downloadRes = await _businessRequestService.DownloadActivityPackage(url, destFile, packageName, system, packageVersion);
                if (!downloadRes.IsSuccess)
                {
                    _remoteLog.AddLog(taskInfo.TaskId, $"当前系统类型{system},下载组件包：{packageName}_{packageVersion}", OperationResult.Fail, OperationType.End);
                    return new(false, $"当前系统类型{system},下载组件包失败:{downloadRes.ErrorMessage}");
                }
                if (!File.Exists(destFile))
                {
                    _logService.LogError($"当前系统类型{system},下载组件包:{packageName}_{packageVersion}失败");
                    _remoteLog.AddLog(taskInfo.TaskId, $"当前系统类型{system},下载组件包：{packageName}_{packageVersion}", OperationResult.Fail, OperationType.End);
                    return new(false, $"当前系统类型{system},下载组件包失败");
                }
                }
                else
                {
                    string md5 = PackagesDownloadCore.Instance.GetMD5Hash(destFile);
                    if (md5 != package.Md5)
                    {
                        string system = RuntimeInfoHelper.GetRuntimeIdentifier();
                        _remoteLog.AddLog(taskInfo.TaskId,
                            $"当前系统类型{system},下载组件包：{packageName}_{packageVersion}，{string.Format("依赖包Md5值比较结果为不相同:{0},{1}",
                                md5, package.Md5)}", OperationResult.Fail, OperationType.End);
                        _logService.LogInfo($"依赖包Md5值比较结果为不相同:{md5},{package.Md5}");
                        await _businessRequestService.DownloadActivityPackage(url, destFile, packageName,
                            system, packageVersion);
                        if (!File.Exists(destFile))
                        {
                            _remoteLog.AddLog(taskInfo.TaskId, $"当前系统类型{system},下载组件包：{packageName}_{packageVersion}",
                                OperationResult.Fail, OperationType.End);
                            return new(false, $"当前系统类型{system},下载组件包失败");
                        }
                    }
                }
                _remoteLog.AddLog(taskInfo.TaskId, $"下载组件包：{packageName}_{packageVersion}", OperationResult.Success, OperationType.End);
            }
            return new(true, string.Empty);
        }

    #endregion


    #region UploadTask
    
        private async Task ResetUploadTaskRepository()
        {
            var uploadTasks = await _uploadTaskRepository.GetAllAsync();
            foreach (var uploadTask in uploadTasks)
            {
                if (string.IsNullOrEmpty(uploadTask.TaskLogContent))
                {
                    TaskLogModel taskLogModel = new TaskLogModel()
                    {
                        State = 2,
                        RpaId = _robotConfig.RobotId,
                        Task_Id = uploadTask.Id,
                        Details = "执行器在流程运行中被关闭",
                        SignGuid = Guid.NewGuid().ToString(),
                        UploadTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fff")
                    };
                    uploadTask.TaskLogContent = JsonConvert.SerializeObject(taskLogModel);
                }
                uploadTask.TaskCompletedTime.Restart();
                _runningContext.UploadRobotTaskDict.TryAdd(uploadTask.Id, uploadTask);
                //仅尝试一次，如果不行就不上传了
                await _uploadTaskRepository.DeleteAsync(uploadTask.Id);
                await UploadTasks();
            }
            _uploadTaskRepository.SaveData();
        }

        public async Task UploadTasks()
        {
            if (_businessRequestService != null)
            {
                var uploadTasks = _runningContext.UploadRobotTaskDict.Values.Select(d => new UploadTaskModel(d.Id, d.Tags)).ToArray();

                await _businessRequestService.UploadExecuteList(_robotConfig.HttpUrl, uploadTasks);
            }
        }

        public void SaveToUploadTaskRepository()
        {
            foreach (var uploadTask in _runningContext.UploadRobotTaskDict.Values)
            {
                _uploadTaskRepository.AddAsync(uploadTask);
            }
            _uploadTaskRepository.SaveData();
        }

    #endregion

    public void Dispose()
    {
        StopPollingAndReconnect();
        _pollingCancellationTokenSource?.Dispose();
    }
}