<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:projectPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
        xmlns:converters="clr-namespace:Skybridge.Robot.Presentation.Converters"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="900"
        x:DataType="projectPage:ScheduleViewModel"
        Title="调度计划配置"
        Width="1000"
        Height="670"
        MinWidth="700" MinHeight="600"
        WindowStartupLocation="CenterOwner"
        Background="#FAFAFA"
        x:Class="Skybridge.Robot.Presentation.Views.ScheduleView">

    <Window.Resources>
        <converters:EnumEqualsConverter x:Key="EnumEqualsConverter"/>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:BoolToBackgroundConverter x:Key="BoolToBackgroundConverter"/>
        <converters:BoolToForegroundConverter x:Key="BoolToForegroundConverter"/>
        <converters:ScheduleTypeConverter x:Key="ScheduleTypeConverter"/>
    </Window.Resources>
    <Window.Styles>
        
        <!-- Modern Card Style -->
        <Style Selector="Border.ModernCard">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="BoxShadow" Value="0 2 8 0 #10000000"/>
        </Style>

        <!-- Section Header Style -->
        <Style Selector="TextBlock.SectionHeader">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#1F2937"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>

        <!-- Field Label Style -->
        <Style Selector="TextBlock.FieldLabel">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>

        <!-- Modern TextBox Style -->
        <Style Selector="TextBox.ModernTextBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="MinHeight" Value="44"/>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style Selector="ComboBox.ModernComboBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="MinHeight" Value="44"/>
        </Style>

        <!-- Primary Button Style -->
        <Style Selector="Button.PrimaryButton">
            <Setter Property="Background" Value="#3B82F6"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="MinHeight" Value="44"/>
        </Style>

        <!-- Secondary Button Style -->
        <Style Selector="Button.SecondaryButton">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="MinHeight" Value="44"/>
        </Style>
    </Window.Styles>
    <!-- Main Layout -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <!-- Content -->
        <ScrollViewer  Grid.Row="0" Padding="32" Background="#FAFAFA">
            <StackPanel Spacing="24" MaxWidth="800">

                <!-- 任务基本信息 -->
                <Border Classes="ModernCard">
                    <StackPanel Spacing="20">
                        <TextBlock Text="📋 任务信息" Classes="SectionHeader"/>

                        <Grid ColumnDefinitions="*,20,*" RowDefinitions="Auto,Auto,Auto,Auto">
                            <!-- Task Name -->
                            <StackPanel Grid.Column="0" Grid.Row="0" Spacing="8">
                                <TextBlock Text="任务名称 *" Classes="FieldLabel"/>
                                <TextBox Text="{Binding TaskName}"
                                        Watermark="请输入任务名称"
                                        Classes="ModernTextBox"/>
                            </StackPanel>

                            <!-- Task Description -->
                            <StackPanel Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="1" Spacing="8" Margin="0,16,0,0">
                                <TextBlock Text="任务描述" Classes="FieldLabel"/>
                                <TextBox Text="{Binding TaskDescription}"
                                        Watermark="请输入任务描述（可选）"
                                        AcceptsReturn="True"
                                        Height="80"
                                        Classes="ModernTextBox"
                                        TextWrapping="Wrap"/>
                            </StackPanel>

                            <!-- Settings Row -->
                            <StackPanel Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="2" Orientation="Horizontal" Spacing="24" Margin="0,16,0,0">
                                <CheckBox IsChecked="{Binding IsEnabled}"
                                         Content="启用任务"
                                         FontSize="14"
                                         FontWeight="Medium"/>
                                <CheckBox IsChecked="{Binding IsDebugLog}"
                                         Content="调试日志"
                                         FontSize="14"
                                         FontWeight="Medium"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 调度类型选择 -->
                <Border Classes="ModernCard">
                    <StackPanel Spacing="20">
                        <TextBlock Text="⏰ 调度类型" Classes="SectionHeader"/>

                        <StackPanel Spacing="12">
                            <TextBlock Text="选择任务执行的时间规则" Classes="FieldLabel"/>
                            <ComboBox ItemsSource="{Binding ScheduleTypes}"
                                      SelectedIndex="{Binding SelectedScheduleTypeIndex}"
                                      SelectedValue="{Binding SelectedScheduleType,Converter={StaticResource ScheduleTypeConverter}}"
                                      Classes="ModernComboBox">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Spacing="4">
                                            <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                            <TextBlock Text="{Binding Description}" FontSize="12" Foreground="#6B7280"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- KVB参数配置 -->
                <Border Classes="ModernCard">
                    <StackPanel Spacing="20">
                        <TextBlock Text="⚙️ 参数配置" Classes="SectionHeader"/>

                        <StackPanel Spacing="12">
                            <TextBlock Text="KVB参数 (键值对)" Classes="FieldLabel"/>
                            <TextBox Text="{Binding KvbParametersText}"
                                    Watermark="请输入参数，格式：key1=value1;key2=value2"
                                    AcceptsReturn="True"
                                    Height="100"
                                    Classes="ModernTextBox"
                                    TextWrapping="Wrap"/>
                            <TextBlock Text="💡 提示：多个参数用分号(;)分隔，键值用等号(=)连接"
                                      FontSize="12"
                                      Foreground="#6B7280"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Cron表达式配置 -->
                <Border Classes="ModernCard"
                       IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Cron}">
                    <StackPanel Spacing="20">
                        <TextBlock Text="🔧 Cron表达式配置" Classes="SectionHeader"/>

                        <StackPanel Spacing="12">
                            <TextBlock Text="Cron表达式 *" Classes="FieldLabel"/>
                            <Grid ColumnDefinitions="*,Auto" ColumnSpacing="12">
                                <TextBox Grid.Column="0"
                                        Text="{Binding CronExpression}"
                                        Watermark="例如: 0 0 * * * (每天午夜执行)"
                                        Classes="ModernTextBox"/>
                                <Button Grid.Column="1"
                                       Content="测试表达式"
                                       Command="{Binding TestCronCommand}"
                                       Classes="SecondaryButton"/>
                            </Grid>
                        </StackPanel>

                        <StackPanel Spacing="12">
                            <TextBlock Text="📚 常用Cron表达式示例" FontWeight="SemiBold" FontSize="14" Foreground="#374151"/>
                            <Border Background="#F9FAFB" CornerRadius="8" Padding="16">
                                <StackPanel Spacing="8">
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12">
                                        <TextBlock Grid.Column="0" Text="0 0 * * *" FontFamily="Consolas" FontSize="12" Foreground="#059669"/>
                                        <TextBlock Grid.Column="1" Text="每天午夜执行" FontSize="12" Foreground="#374151"/>
                                    </Grid>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12">
                                        <TextBlock Grid.Column="0" Text="0 */6 * * *" FontFamily="Consolas" FontSize="12" Foreground="#059669"/>
                                        <TextBlock Grid.Column="1" Text="每6小时执行一次" FontSize="12" Foreground="#374151"/>
                                    </Grid>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12">
                                        <TextBlock Grid.Column="0" Text="0 0 * * 1" FontFamily="Consolas" FontSize="12" Foreground="#059669"/>
                                        <TextBlock Grid.Column="1" Text="每周一午夜执行" FontSize="12" Foreground="#374151"/>
                                    </Grid>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12">
                                        <TextBlock Grid.Column="0" Text="0 0 1 * *" FontFamily="Consolas" FontSize="12" Foreground="#059669"/>
                                        <TextBlock Grid.Column="1" Text="每月1号午夜执行" FontSize="12" Foreground="#374151"/>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 间隔时间配置 -->
                <Border Classes="ModernCard"
                       IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Interval}">
                    <StackPanel Spacing="20">
                        <TextBlock Text="⏱️ 间隔时间配置" Classes="SectionHeader"/>

                        <StackPanel Spacing="12">
                            <TextBlock Text="间隔秒数 *" Classes="FieldLabel"/>
                            <NumericUpDown Value="{Binding IntervalSeconds}"
                                          Minimum="1"
                                          Maximum="86400"
                                          Increment="1"
                                          FormatString="N0"
                                          Classes="ModernTextBox"
                                          Height="44"/>
                            <TextBlock Text="💡 范围: 1-86400秒 (1秒到24小时)"
                                      FontSize="12"
                                      Foreground="#6B7280"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 每日执行配置 -->
                <Border Classes="ModernCard"
                       IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Daily}">
                    <StackPanel Spacing="20">
                        <TextBlock Text="📅 每日执行配置" Classes="SectionHeader"/>

                        <Grid ColumnDefinitions="*,20,*" RowDefinitions="Auto,Auto">
                            <StackPanel Grid.Column="0" Spacing="8">
                                <TextBlock Text="小时 *" Classes="FieldLabel"/>
                                <ComboBox ItemsSource="{Binding Hours}"
                                         SelectedItem="{Binding DailyHour}"
                                         Classes="ModernComboBox"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2" Spacing="8">
                                <TextBlock Text="分钟 *" Classes="FieldLabel"/>
                                <ComboBox ItemsSource="{Binding Minutes}"
                                         SelectedItem="{Binding DailyMinute}"
                                         Classes="ModernComboBox"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 每小时执行配置 -->
                <Border Classes="ModernCard"
                       IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Hourly}">
                    <StackPanel Spacing="20">
                        <TextBlock Text="🕐 每小时执行配置" Classes="SectionHeader"/>

                        <StackPanel Spacing="12">
                            <TextBlock Text="分钟 *" Classes="FieldLabel"/>
                            <ComboBox ItemsSource="{Binding Minutes}"
                                     SelectedItem="{Binding HourlyMinute}"
                                     Classes="ModernComboBox"/>
                            <TextBlock Text="💡 每小时的第几分钟执行任务"
                                      FontSize="12"
                                      Foreground="#6B7280"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 时间范围配置 -->
                <Border Classes="ModernCard">
                    <StackPanel Spacing="20">
                        <TextBlock Text="📆 时间范围" Classes="SectionHeader"/>

                        <Grid ColumnDefinitions="*,20,*" RowDefinitions="Auto,Auto,Auto">
                            <StackPanel Grid.Column="0" Spacing="8">
                                <TextBlock Text="开始时间" Classes="FieldLabel"/>
                                <DatePicker SelectedDate="{Binding StartTime}"
                                           Classes="ModernComboBox"/>
                            </StackPanel>

                            <StackPanel Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="1" Margin="0,16,0,0">
                                <CheckBox IsChecked="{Binding HasEndTime}"
                                         Content="设置结束时间"
                                         FontSize="14"
                                         FontWeight="Medium"/>
                            </StackPanel>

                            <StackPanel Grid.Column="0" Grid.Row="2" Spacing="8"
                                       IsVisible="{Binding HasEndTime}"
                                       Margin="0,12,0,0">
                                <TextBlock Text="结束时间" Classes="FieldLabel"/>
                                <DatePicker SelectedDate="{Binding EndTime}"
                                           Classes="ModernComboBox"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- 验证信息 -->
                <Border Classes="ModernCard">
                    <StackPanel Spacing="20">
                        <TextBlock Text="✅ 配置验证" Classes="SectionHeader"/>

                        <Border CornerRadius="8" Padding="16">
                            <Border.Background>
                                <SolidColorBrush Color="{Binding HasValidationError, Converter={StaticResource BoolToBackgroundConverter}}"/>
                            </Border.Background>
                            <StackPanel Spacing="12">
                                <TextBlock Text="{Binding ValidationMessage}"
                                          Foreground="{Binding HasValidationError, Converter={StaticResource BoolToForegroundConverter}}"
                                          TextWrapping="Wrap"
                                          FontSize="14"/>

                                <Button Content="验证配置"
                                       Command="{Binding ValidateCommand}"
                                       Classes="SecondaryButton"
                                       HorizontalAlignment="Left"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer Actions -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0">
            <Grid Margin="32,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                

                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="12">
                    <Button Content="取消"
                           Click="CancelButton_Click"
                           Classes="SecondaryButton"
                           Width="100"/>
                    <Button Content="保存"
                           Command="{Binding SaveCommand}"
                           Classes="PrimaryButton"
                           Width="100"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>

</Window>
