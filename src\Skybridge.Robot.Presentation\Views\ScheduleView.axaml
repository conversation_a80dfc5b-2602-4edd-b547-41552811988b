<Window xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:projectPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
             xmlns:converters="clr-namespace:Skybridge.Robot.Presentation.Converters"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="700"
             x:DataType="projectPage:ScheduleViewModel"
             Title="调度计划配置"
             
             x:Class="Skybridge.Robot.Presentation.Views.ScheduleView">

    <Window.Resources>
        <converters:EnumEqualsConverter x:Key="EnumEqualsConverter"/>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:BoolToBackgroundConverter x:Key="BoolToBackgroundConverter"/>
        <converters:BoolToForegroundConverter x:Key="BoolToForegroundConverter"/>
        <converters:ScheduleTypeConverter x:Key="ScheduleTypeConverter"/>
    </Window.Resources>
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <ScrollViewer Grid.Row="0" >
            <StackPanel Margin="20" Spacing="15">

            <!-- 任务基本信息 -->
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="15">
            <StackPanel Spacing="10">
            <TextBlock Text="任务信息" FontWeight="Bold" FontSize="16"/>

            <StackPanel Spacing="5">
                <TextBlock Text="任务名称*"/>
                <TextBox Text="{Binding TaskName}" Watermark="请输入任务名称"/>
            </StackPanel>

            <StackPanel Spacing="5">
                <TextBlock Text="任务描述"/>
                <TextBox Text="{Binding TaskDescription}"
                         Watermark="请输入任务描述（可选）"
                         AcceptsReturn="True"
                         Height="60"/>
            </StackPanel>

            <CheckBox IsChecked="{Binding IsEnabled}" Content="启用任务"/>
            </StackPanel>
            </Border>

            <!-- 调度类型选择 -->
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="15">
            <StackPanel Spacing="10">
            <TextBlock Text="调度类型" FontWeight="Bold" FontSize="16"/>

            <ComboBox ItemsSource="{Binding ScheduleTypes}"
                      SelectedIndex="{Binding SelectedScheduleTypeIndex}"
                      SelectedValue="{Binding SelectedScheduleType,Converter={StaticResource ScheduleTypeConverter}}" 
                      HorizontalAlignment="Stretch">
                <ComboBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding Name}"></TextBlock>
                    </DataTemplate>    
                </ComboBox.ItemTemplate>
            </ComboBox>
            </StackPanel>
            </Border>

            <!-- Cron表达式配置 -->
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="15"
            IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Cron}">
            <StackPanel Spacing="10">
            <TextBlock Text="Cron表达式配置" FontWeight="Bold" FontSize="16"/>

            <StackPanel Spacing="5">
                <TextBlock Text="Cron表达式*"/>
                <Grid ColumnDefinitions="*,Auto">
                    <TextBox Grid.Column="0"
                             Text="{Binding CronExpression}"
                             Watermark="例如: 0 0 * * * (每天午夜执行)"/>
                    <Button Grid.Column="1"
                            Content="测试"
                            Command="{Binding TestCronCommand}"
                            Margin="5,0,0,0"/>
                </Grid>
            </StackPanel>

            <TextBlock Text="常用Cron表达式示例:" FontWeight="SemiBold"/>
            <StackPanel Spacing="2">
                <TextBlock Text="• 0 0 * * * - 每天午夜执行" FontSize="12" Foreground="Gray"/>
                <TextBlock Text="• 0 */6 * * * - 每6小时执行一次" FontSize="12" Foreground="Gray"/>
                <TextBlock Text="• 0 0 * * 1 - 每周一午夜执行" FontSize="12" Foreground="Gray"/>
                <TextBlock Text="• 0 0 1 * * - 每月1号午夜执行" FontSize="12" Foreground="Gray"/>
            </StackPanel>
            </StackPanel>
            </Border>

            <!-- 间隔时间配置 -->
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="15"
            IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Interval}">
            <StackPanel Spacing="10">
            <TextBlock Text="间隔时间配置" FontWeight="Bold" FontSize="16"/>

            <StackPanel Spacing="5">
                <TextBlock Text="间隔秒数*"/>
                <NumericUpDown Value="{Binding IntervalSeconds}"
                               Minimum="1"
                               Maximum="86400"
                               Increment="1"
                               FormatString="N0"/>
                <TextBlock Text="范围: 1-86400秒 (1秒到24小时)" FontSize="12" Foreground="Gray"/>
            </StackPanel>
            </StackPanel>
            </Border>

            <!-- 每日执行配置 -->
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="15"
            IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Daily}">
            <StackPanel Spacing="10">
            <TextBlock Text="每日执行配置" FontWeight="Bold" FontSize="16"/>

            <Grid ColumnDefinitions="*,20,*" RowDefinitions="Auto,Auto">
                <StackPanel Grid.Column="0" Spacing="5">
                    <TextBlock Text="小时*"/>
                    <ComboBox ItemsSource="{Binding Hours}"
                              SelectedItem="{Binding DailyHour}"
                              HorizontalAlignment="Stretch"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Spacing="5">
                    <TextBlock Text="分钟*"/>
                    <ComboBox ItemsSource="{Binding Minutes}"
                              SelectedItem="{Binding DailyMinute}"
                              HorizontalAlignment="Stretch"/>
                </StackPanel>
            </Grid>
            </StackPanel>
            </Border>

            <!-- 每小时执行配置 -->
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="15"
            IsVisible="{Binding SelectedScheduleType, Converter={StaticResource EnumEqualsConverter}, ConverterParameter=Hourly}">
            <StackPanel Spacing="10">
            <TextBlock Text="每小时执行配置" FontWeight="Bold" FontSize="16"/>

            <StackPanel Spacing="5">
                <TextBlock Text="分钟*"/>
                <ComboBox ItemsSource="{Binding Minutes}"
                          SelectedItem="{Binding HourlyMinute}"
                          HorizontalAlignment="Stretch"/>
                <TextBlock Text="每小时的第几分钟执行任务" FontSize="12" Foreground="Gray"/>
            </StackPanel>
            </StackPanel>
            </Border>

            <!-- 时间范围配置 -->
            <Border BorderBrush="LightGray" BorderThickness="1" CornerRadius="5" Padding="15">
            <StackPanel Spacing="10">
            <TextBlock Text="时间范围" FontWeight="Bold" FontSize="16"/>

            <StackPanel Spacing="5">
                <TextBlock Text="开始时间"/>
                <DatePicker SelectedDate="{Binding StartTime}"/>
            </StackPanel>

            <CheckBox IsChecked="{Binding HasEndTime}" Content="设置结束时间"/>

            <StackPanel Spacing="5" IsVisible="{Binding HasEndTime}">
                <TextBlock Text="结束时间"/>
                <DatePicker SelectedDate="{Binding EndTime}"/>
            </StackPanel>
            </StackPanel>
            </Border>

            <!-- 验证信息 -->
            <Border BorderBrush="{Binding HasValidationError, Converter={StaticResource BoolToColorConverter}}"
            BorderThickness="1"
            CornerRadius="5"
            Padding="15"
            Background="{Binding HasValidationError, Converter={StaticResource BoolToBackgroundConverter}}">
            <StackPanel Spacing="10">
            <TextBlock Text="配置验证" FontWeight="Bold" FontSize="16"/>

            <TextBlock Text="{Binding ValidationMessage}"
                       Foreground="{Binding HasValidationError, Converter={StaticResource BoolToForegroundConverter}}"
                       TextWrapping="Wrap"/>

            <Button Content="验证配置"
                    Command="{Binding ValidateCommand}"
                    HorizontalAlignment="Left"/>
            </StackPanel>
            </Border>

            </StackPanel>
        </ScrollViewer>
        <Border Height="60" Background="#F3F3F3" Grid.Row="1">
            <Button Width="100"
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Center" 
                    Content="保存"
                    Command="{Binding SaveCommand}">
            </Button>
        </Border>
    </Grid>

</Window>
