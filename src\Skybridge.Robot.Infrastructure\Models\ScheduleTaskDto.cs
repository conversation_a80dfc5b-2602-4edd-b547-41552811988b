﻿using Skybridge.Domain.Core.Model;

namespace Skybridge.Robot.Infrastructure.Models;

public class ScheduleTaskDto
{
    public string ProjectId { get; set; }
    public string TaskId { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public ScheduleRule ScheduleRule { get; set; }
    
    public ScheduleType ScheduleType { get; set; }
    public bool IsEnabled { get; set; }
    public DateTime? LastExecutionTime { get; set; }
    
    public bool IsLogDebug { get; set; }
    
    public Dictionary<string, string>? KvbParameters { get; set; }

    /// <summary>
    /// 任务状态
    /// </summary>
    public ScheduleTaskStatus Status { get; set; }

    /// <summary>
    /// 最后一次执行结果
    /// </summary>
    public string? LastExecutionResult { get; set; }

    /// <summary>
    /// 最后一次执行异常
    /// </summary>
    public Exception? LastExecutionException { get; set; }

    /// <summary>
    /// 任务创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 任务更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; }

    /// <summary>
    /// 执行次数
    /// </summary>
    public int ExecutionCount { get; set; }

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public int FailureCount { get; set; }


    public IScheduledTaskInfo ToScheduledTaskInfo()
    {
        return new ScheduledTaskInfo(ProjectId,TaskId,Name,Description,ScheduleRule,ScheduleType,IsLogDebug,KvbParameters,IsEnabled)
        {
            Name = this.Name,
            ScheduleRule = this.ScheduleRule,
            IsEnabled = this.IsEnabled,
            LastExecutionTime = this.LastExecutionTime,
            Status = this.Status,
            LastExecutionResult = this.LastExecutionResult,
            LastExecutionException = this.LastExecutionException,
            UpdatedTime = this.UpdatedTime,
            ExecutionCount = this.ExecutionCount,
            SuccessCount = this.SuccessCount,
            FailureCount = this.FailureCount
        };
    }
    
}