<Window xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:projectPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
             xmlns:converters="clr-namespace:Skybridge.Robot.Presentation.Converters"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             Width="800" Height="500"
             WindowStartupLocation="CenterOwner"
             x:Class="Skybridge.Robot.Presentation.Views.ScheduleListView"
             d:DataContext="{d:DesignInstance Type=projectPage:ScheduleListViewModel, IsDesignTimeCreatable=True}"
             x:DataType="projectPage:ScheduleListViewModel">

    <Window.Resources>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:BoolToStatusTextConverter x:Key="BoolToStatusTextConverter"/>
    </Window.Resources>
    <Window.Styles>
        <Style Selector="Border.ModernCard">
            <Setter  Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="BoxShadow" Value="0 2 8 0 #10000000"/>
        </Style>
    </Window.Styles>
    <DockPanel Margin="25" LastChildFill="True">
        <TextBlock Margin="0 5" DockPanel.Dock="Top" FontSize="16" FontWeight="SemiBold">调度计划列表</TextBlock>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>

            <!-- Modern DataGrid instead of ListBox -->
            <Border Grid.Row="0" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8" Background="White">
                <DataGrid ItemsSource="{Binding Schedules}"
                         AutoGenerateColumns="False"
                         CanUserReorderColumns="False"
                         CanUserResizeColumns="True"
                         CanUserSortColumns="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         Background="Transparent"
                         RowBackground="Transparent">
                    <!-- <DataGrid.Styles> -->
                    <!--     <Style Selector="DataGridColumnHeader"> -->
                    <!--         <Setter Property="Background" Value="#F5F5F5"/> -->
                    <!--         <Setter Property="Foreground" Value="#333333"/> -->
                    <!--         <Setter Property="FontWeight" Value="SemiBold"/> -->
                    <!--         <Setter Property="Padding" Value="12,8"/> -->
                    <!--         <Setter Property="BorderBrush" Value="#E0E0E0"/> -->
                    <!--         <Setter Property="BorderThickness" Value="0,0,1,1"/> -->
                    <!--     </Style> -->
                    <!-- </DataGrid.Styles> -->
                    <DataGrid.Columns>
                        <!-- Task Name Column -->
                        <DataGridTextColumn Header="任务名称"
                                          Binding="{Binding TaskName}"
                                          Width="2*"
                                          MinWidth="150">
                        </DataGridTextColumn>

                        <!-- Schedule Type Column -->
                        <DataGridTextColumn Header="调度类型"
                                          Binding="{Binding SelectedScheduleType}"
                                          Width="1*"
                                          MinWidth="100">
                        </DataGridTextColumn>

                        <!-- Status Column -->
                        <DataGridTemplateColumn Header="状态" Width="100" MinWidth="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                        <Border.Background>
                                            <SolidColorBrush Color="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}}"/>
                                        </Border.Background>
                                        <TextBlock Text="{Binding IsEnabled, Converter={StaticResource BoolToStatusTextConverter}}"
                                                 Foreground="White"
                                                 FontSize="11"
                                                 FontWeight="Medium"
                                                 HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Operations Column -->
                        <DataGridTemplateColumn Header="操作" Width="120" MinWidth="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="8">
                                        <Button Content="编辑"
                                              CommandParameter="{Binding}"
                                              Classes="accent"
                                              Padding="12,6"
                                              FontSize="12"
                                              CornerRadius="4">
                                        </Button>
                                        <Button Content="删除"
                                              CommandParameter="{Binding}"
                                              Classes="danger"
                                              Padding="12,6"
                                              FontSize="12"
                                              CornerRadius="4">
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>

                    <DataGrid.Styles>
                        <Style Selector="DataGridRow">
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="MinHeight" Value="48"/>
                        </Style>
                        <Style Selector="DataGridCell">
                            <Setter Property="Padding" Value="12,8"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                        </Style>
                        <Style Selector="Button.accent">
                            <Setter Property="Background" Value="#007ACC"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                        </Style>
                        <Style Selector="Button.accent:pointerover">
                            <Setter Property="Background" Value="#005A9E"/>
                        </Style>
                        <Style Selector="Button.danger">
                            <Setter Property="Background" Value="#DC3545"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                        </Style>
                        <Style Selector="Button.danger:pointerover">
                            <Setter Property="Background" Value="#C82333"/>
                        </Style>
                    </DataGrid.Styles>
                </DataGrid>
            </Border>

            <Button Margin="0,20,0,0"
                   HorizontalAlignment="Right"
                   Grid.Row="1"
                   Content="新增规则"
                   Command="{Binding CreateScheduleCommand}"
                   Classes="primary"
                   Padding="16,10"
                   FontSize="14"
                   CornerRadius="6">
            </Button>
        </Grid>

    </DockPanel>
</Window>
