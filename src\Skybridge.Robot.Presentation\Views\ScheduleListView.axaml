﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:projectPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Skybridge.Robot.Presentation.Views.ScheduleListView"
             d:DataContext="{d:DesignInstance Type=projectPage:ScheduleListViewModel, IsDesignTimeCreatable=True}"
             x:DataType="projectPage:ScheduleListViewModel">
    <DockPanel Margin="25" LastChildFill="True">
        <TextBlock Margin="0 5" DockPanel.Dock="Top">调度计划列表：</TextBlock>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>
            <ListBox Grid.Row="0"  ItemsSource="{Binding Schedules}" >
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <TextBlock Text="{Binding TaskName}"></TextBlock>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
            <Button Margin="0,20,0,0" HorizontalAlignment="Right" Grid.Row="1" Content="新增规则" Command="{Binding CreateScheduleCommand}"></Button>
        </Grid>

    </DockPanel>
</UserControl>
