<Window xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:projectPage="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
             xmlns:converters="clr-namespace:Skybridge.Robot.Presentation.Converters"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             Width="800" Height="500"
             WindowStartupLocation="CenterOwner"
             x:CompileBindings="False"
             x:Class="Skybridge.Robot.Presentation.Views.ScheduleListView"
             d:DataContext="{d:DesignInstance Type=projectPage:ScheduleListViewModel, IsDesignTimeCreatable=True}"
             x:DataType="projectPage:ScheduleListViewModel">

    <Window.Resources>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:BoolToStatusTextConverter x:Key="BoolToStatusTextConverter"/>
    </Window.Resources>
    <Window.Styles>
        <Style Selector="Border.ModernCard">
            <Setter  Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="BoxShadow" Value="0 2 8 0 #10000000"/>
        </Style>
    </Window.Styles>
    <DockPanel Margin="25" LastChildFill="True">
        <TextBlock Margin="0 5" DockPanel.Dock="Top" FontSize="16" FontWeight="SemiBold">调度计划列表</TextBlock>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>

            <!-- Modern DataGrid instead of ListBox -->
            <Border Grid.Row="0" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="4" Background="White">
                <DataGrid ItemsSource="{Binding Schedules}"
                         AutoGenerateColumns="False"
                         CanUserReorderColumns="False"
                         CanUserResizeColumns="True"
                         CanUserSortColumns="True"
                         GridLinesVisibility="None"
                         HeadersVisibility="Column"
                         IsReadOnly="True"
                         Focusable="False"
                         SelectionMode="Single"
                         Background="Transparent"
                         RowBackground="White"
                         BorderThickness="0">
                    <DataGrid.Columns>
                        <!-- Task Name Column -->
                        <DataGridTextColumn Header="任务名称"
                                          Binding="{Binding TaskName}"
                                          Width="2*"
                                          MinWidth="150">
                        </DataGridTextColumn>

                        <!-- Schedule Type Column -->
                        <DataGridTextColumn Header="调度类型"
                                          Binding="{Binding SelectedScheduleType}"
                                          Width="1*"
                                          MinWidth="100">
                        </DataGridTextColumn>

                        <!-- Status Column -->
                        <DataGridTemplateColumn Header="状态" Width="120" MinWidth="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border HorizontalAlignment="Left" Margin="16,0,0,0">
                                        <Border.Background>
                                            <SolidColorBrush Color="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}}"/>
                                        </Border.Background>
                                        <TextBlock Text="{Binding IsEnabled, Converter={StaticResource BoolToStatusTextConverter}}"
                                                   Foreground="#262626"
                                                   FontSize="14"
                                                   FontWeight="Medium"
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- Operations Column -->
                        <DataGridTemplateColumn Header="操作" Width="120" MinWidth="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Left" Spacing="8">
                                        <Button Content="编辑" 
                                                Command="{Binding DataContext.EditScheduleCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                              CommandParameter="{Binding}"
                                              Classes="accent"
                                              Padding="12,6"
                                              FontSize="12"
                                              CornerRadius="4">
                                        </Button>
                                        <Button Content="删除"
                                                Command="{Binding DataContext.DeleteScheduleCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                              CommandParameter="{Binding}"
                                              Classes="danger"
                                              Padding="12,6"
                                              FontSize="12"
                                              CornerRadius="4">
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>

                    <DataGrid.Styles>
                        <!-- Enhanced DataGrid Row Styling -->
                        <Style Selector="DataGridRow">
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0,0,0,1"/>
                            <Setter Property="BorderBrush" Value="#F0F0F0"/>
                        </Style>

                        <!-- Hover Effect for Rows -->
                        <Style Selector="DataGridRow:pointerover">
                            <Setter Property="Background" Value="#F8F9FA"/>
                        </Style>

                        <!-- Enhanced Selected Row Styling -->
                        <Style Selector="DataGridRow:selected">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="0%,100%">
                                        <GradientStop Color="#E3F2FD" Offset="0"/>
                                        <GradientStop Color="#BBDEFB" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="BorderBrush" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0,1,0,1"/>
                        </Style>

                        <!-- Selected Row Hover Effect -->
                        <Style Selector="DataGridRow:selected:pointerover">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="0%,100%">
                                        <GradientStop Color="#E1F5FE" Offset="0"/>
                                        <GradientStop Color="#B3E5FC" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Style>

                        <!-- Enhanced Cell Styling -->
                        <Style Selector="DataGridCell">
                            <Setter Property="Padding" Value="0,0"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Foreground" Value="#262626"/>
                        </Style>
                        

                        <!-- Selected Cell Text Contrast -->
                        <Style Selector="DataGridRow:selected DataGridCell">
                            <Setter Property="Background" Value="#EFF0F1"></Setter>
                            <Setter Property="Foreground" Value="#262626"/>
                            <Setter Property="FontWeight" Value="Medium"/>
                        </Style>

                        <!-- Enhanced Column Header Styling -->
                        <Style Selector="DataGridColumnHeader">
                            <Setter Property="Background" Value="#F8F9FA"/>
                            <Setter Property="Foreground" Value="#1F2937"/>
                            <Setter Property="FontWeight" Value="SemiBold"/>
                            <Setter Property="FontSize" Value="14"/>
                            <Setter Property="Padding" Value="16,12"/>
                            <Setter Property="BorderBrush" Value="#E5E7EB"/>
                            <Setter Property="BorderThickness" Value="0,0,1,2"/>
                            <Setter Property="HorizontalContentAlignment" Value="Left"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                        </Style>

                        <!-- Column Header Hover Effect -->
                        <Style Selector="DataGridColumnHeader:pointerover">
                            <Setter Property="Background" Value="#F3F4F6"/>
                        </Style>

                        <!-- Enhanced Button Styling -->
                        <Style Selector="Button.accent">
                            <Setter Property="Background" Value="#3B82F6"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="CornerRadius" Value="6"/>
                            <Setter Property="FontWeight" Value="Medium"/>
                            <Setter Property="FontSize" Value="12"/>
                            <Setter Property="Padding" Value="12,6"/>
                            <Setter Property="Margin" Value="2"/>
                        </Style>

                        <Style Selector="Button.accent:pointerover">
                            <Setter Property="Background" Value="#2563EB"/>
                        </Style>

                        <Style Selector="Button.accent:pressed">
                            <Setter Property="Background" Value="#1D4ED8"/>
                        </Style>

                        <Style Selector="Button.danger">
                            <Setter Property="Background" Value="#EF4444"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="CornerRadius" Value="6"/>
                            <Setter Property="FontWeight" Value="Medium"/>
                            <Setter Property="FontSize" Value="12"/>
                            <Setter Property="Padding" Value="12,6"/>
                            <Setter Property="Margin" Value="2"/>
                        </Style>

                        <Style Selector="Button.danger:pointerover">
                            <Setter Property="Background" Value="#DC2626"/>
                        </Style>

                        <Style Selector="Button.danger:pressed">
                            <Setter Property="Background" Value="#B91C1C"/>
                        </Style>
                    </DataGrid.Styles>
                </DataGrid>
            </Border>

            <Button Margin="0,20,0,0"
                   HorizontalAlignment="Right"
                   Grid.Row="1"
                   Content="新增规则"
                   Command="{Binding CreateScheduleCommand}"
                   Classes="primary"
                   Padding="16,10"
                   FontSize="14"
                   CornerRadius="6">
            </Button>
        </Grid>

    </DockPanel>
</Window>
