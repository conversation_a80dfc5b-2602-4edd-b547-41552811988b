﻿using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Skybridge.Domain.Core.Model;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Models;

namespace Skybridge.Robot.Infrastructure.ExternalServices;

/// <summary>
/// 调度管理器实现类
/// </summary>
public class ScheduleManager : IScheduleManager, IDisposable
{
    private readonly ConcurrentDictionary<string, ScheduledTaskInfo> _tasks;
    private readonly Timer _schedulerTimer;
    private readonly ScheduleSetting _scheduleSetting;
    private readonly ILogService _loggerService;
    private readonly object _lockObject = new object();
    private bool _isRunning;
    private bool _disposed;

    // 调度检查间隔（秒）
    private const int SCHEDULER_INTERVAL_SECONDS = 1;
    


    public ScheduleManager(ScheduleSetting scheduleSetting,
        ILogService loggerService)
    {
        _tasks = new ConcurrentDictionary<string, ScheduledTaskInfo>();
        _scheduleSetting = scheduleSetting;
        _loggerService = loggerService;
        _isRunning = false;

        // 创建定时器但不启动
        _schedulerTimer = new Timer(CheckAndExecuteTasks, null, Timeout.Infinite, Timeout.Infinite);
        LoadSchedules();
        //TODO 需要根据在线离线启动和关闭这些调度计划
        StartManager();
    }

    public void LoadSchedules()
    {
        foreach (var info in _scheduleSetting.ScheduledTaskInfos)
        {
            AddTask(info.Value.ToScheduledTaskInfo());
        }
    }

    #region 任务管理

    public string AddTask(IScheduledTaskInfo taskInfo)
    {
        if (taskInfo == null)
            throw new ArgumentNullException(nameof(taskInfo));

        if (!(taskInfo is ScheduledTaskInfo scheduledTask))
        {
            // 如果传入的不是具体实现类，创建一个新的实例
            scheduledTask = new ScheduledTaskInfo(
                taskInfo.ProjectId,
                taskInfo.TaskId,
                taskInfo.Name,
                taskInfo.Description,
                taskInfo.ScheduleRule,
                taskInfo.ScheduleType,
                taskInfo.IsLogDebug,
                taskInfo.KvbParameters,
                taskInfo.IsEnabled);
        }

        if (_tasks.ContainsKey(scheduledTask.TaskId))
        {
            throw new InvalidOperationException($"任务ID '{scheduledTask.TaskId}' 已存在");
        }

        _tasks.TryAdd(scheduledTask.TaskId, scheduledTask);
        OnTaskCountChanged?.Invoke(_tasks.Count);
        ProjectTaskCountChanged?.Invoke(scheduledTask.ProjectId,_tasks.Count);
        
        _loggerService.LogInfo($"添加调度任务: {scheduledTask.Name} ({scheduledTask.TaskId})");
        return scheduledTask.TaskId;
    }

    public bool RemoveTask(string taskId)
    {
        if (string.IsNullOrWhiteSpace(taskId))
            return false;

        if (_tasks.TryRemove(taskId, out var task))
        {
            OnTaskCountChanged?.Invoke(_tasks.Count);
            ProjectTaskCountChanged?.Invoke(task.ProjectId,_tasks.Count);
            task.Stop();
            _loggerService.LogInfo($"移除调度任务: {task.Name} ({taskId})");
            return true;
        }

        return false;
    }

    public bool UpdateTaskSchedule(string taskId, IScheduleRule newSchedule)
    {
        if (string.IsNullOrWhiteSpace(taskId) || newSchedule == null)
            return false;

        if (_tasks.TryGetValue(taskId, out var task))
        {
            lock (_lockObject)
            {
                task.ScheduleRule = newSchedule;
                task.UpdateNextExecutionTime();
                _loggerService.LogInfo($"更新任务调度规则: {task.Name} ({taskId})");
                return true;
            }
        }

        return false;
    }

    public IEnumerable<IScheduledTaskInfo> GetAllTasks()
    {
        return _tasks.Values.ToList();
    }

    public IScheduledTaskInfo GetTaskById(string taskId)
    {
        if (string.IsNullOrWhiteSpace(taskId))
            return null;

        _tasks.TryGetValue(taskId, out var task);
        return task;
    }

    public event Action<int>? OnTaskCountChanged;
    public event Action<string,int>? ProjectTaskCountChanged;

    #endregion

    #region 任务状态控制

    public bool StartTask(string taskId)
    {
        if (string.IsNullOrWhiteSpace(taskId))
            return false;

        if (_tasks.TryGetValue(taskId, out var task))
        {
            lock (_lockObject)
            {
                task.Enable();
                _loggerService.LogInfo($"启动任务: {task.Name} ({taskId})");
                return true;
            }
        }

        return false;
    }

    public bool PauseTask(string taskId)
    {
        if (string.IsNullOrWhiteSpace(taskId))
            return false;

        if (_tasks.TryGetValue(taskId, out var task))
        {
            lock (_lockObject)
            {
                task.Disable();
                _loggerService.LogInfo($"暂停任务: {task.Name} ({taskId})");
                return true;
            }
        }

        return false;
    }

    public async Task ExecuteTaskImmediatelyAsync(string taskId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(taskId))
            throw new ArgumentException("任务ID不能为空", nameof(taskId));

        if (!_tasks.TryGetValue(taskId, out var task))
            throw new InvalidOperationException($"任务 '{taskId}' 不存在");

        await ExecuteTaskAsync(task);
    }

    public TaskStatus GetTaskStatus(string taskId)
    {
        if (string.IsNullOrWhiteSpace(taskId))
            return TaskStatus.Faulted;

        if (_tasks.TryGetValue(taskId, out var task))
        {
            return task.Status switch
            {
                ScheduleTaskStatus.NotStarted => TaskStatus.Created,
                ScheduleTaskStatus.Running => TaskStatus.WaitingToRun,
                ScheduleTaskStatus.Executing => TaskStatus.Running,
                ScheduleTaskStatus.Paused => TaskStatus.WaitingToRun,
                ScheduleTaskStatus.Stopped => TaskStatus.Canceled,
                ScheduleTaskStatus.Error => TaskStatus.Faulted,
                _ => TaskStatus.Created
            };
        }

        return TaskStatus.Faulted;
    }

    #endregion

    #region 管理器控制

    public void StartManager()
    {
        lock (_lockObject)
        {
            if (_isRunning)
                return;

            _isRunning = true;
            _schedulerTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(SCHEDULER_INTERVAL_SECONDS));
            _loggerService.LogInfo("调度管理器已启动");
        }
    }

    public void StopManager()
    {
        lock (_lockObject)
        {
            if (!_isRunning)
                return;

            _isRunning = false;
            _schedulerTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _loggerService.LogInfo("调度管理器已停止");
        }
    }

    public bool IsManagerRunning()
    {
        return _isRunning;
    }

    public Action<IScheduledTaskInfo> ScheduleStartTask { get; set; }

    #endregion

    #region 事件

    public event Func<string, Task>? TaskExecuting;
    public event Func<string, Task>? TaskExecuted;
    public event Func<string, Exception, Task>? TaskFailed;

    #endregion

    #region 私有方法

    /// <summary>
    /// 定时检查并执行任务
    /// </summary>
    /// <param name="state">状态对象</param>
    private async void CheckAndExecuteTasks(object? state)
    {
        if (!_isRunning || _disposed)
            return;

        try
        {
            var tasksToExecute = _tasks.Values
                .Where(task => task.ShouldExecute())
                .ToList();

            foreach (var task in tasksToExecute)
            {
                // 异步执行任务，不等待完成
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ExecuteTaskAsync(task);
                    }
                    catch (Exception ex)
                    {
                        _loggerService.LogError($"执行任务时发生异常: {task.Name} ({task.TaskId}) {ex}");
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _loggerService?.LogError( $"检查调度任务时发生异常:{ex}");
        }
    }

    /// <summary>
    /// 执行单个任务
    /// </summary>
    /// <param name="task">要执行的任务</param>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ExecuteTaskAsync(ScheduledTaskInfo task)
    {
        if (task.Status == ScheduleTaskStatus.Executing)
        {
            _loggerService?.LogWarning($"任务 {task.Name} ({task.TaskId}) 正在执行中，跳过本次调度");
            return;
        }

        try
        {
            // 触发执行前事件
            if (TaskExecuting != null)
            {
                await TaskExecuting.Invoke(task.TaskId);
            }

            // 记录执行开始
            task.RecordExecutionStart();
            _loggerService.LogInfo($"开始执行调度计划: {task.Name} ({task.TaskId})");
            
            ScheduleStartTask?.Invoke(task);
            // 记录执行成功
            task.RecordExecutionSuccess("调度计划触发成功");
            _loggerService.LogInfo($"调度计划触发成功: {task.Name} ({task.TaskId})");

            // 触发执行成功事件
            if (TaskExecuted != null)
            {
                await TaskExecuted.Invoke(task.TaskId);
            }
        }
        catch (Exception ex)
        {
            // 记录执行失败
            task.RecordExecutionFailure(ex);
            _loggerService.LogError($"任务执行失败: {task.Name} ({task.TaskId}) {ex}");

            // 触发执行失败事件
            if (TaskFailed != null)
            {
                await TaskFailed.Invoke(task.TaskId, ex);
            }
        }
    }

    #endregion

    #region IDisposable

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            StopManager();
            _schedulerTimer?.Dispose();
            _disposed = true;
        }
    }

    #endregion
}