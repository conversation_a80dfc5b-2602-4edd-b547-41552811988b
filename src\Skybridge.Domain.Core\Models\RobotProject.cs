using Avalonia.OpenGL;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Skybridge.Domain.Core.Model;

namespace Skybridge.Domain.Core.Models;

/// <summary>
/// 机器人项目模型
/// </summary>
public class RobotProject : IEntity
{
        /// <summary>
        /// 本地的时候使用项目文件中的projectid，远程的时候使用项目服务端返回的id
        /// </summary>
        public string Id { get; }

        /// <summary>
        /// project_content_id 标识项目唯一的版本号
        /// </summary>
        public string ProjectContentId { get; }

        /// <summary>
        /// 分类：1：普通项目 2：分组项目 3：编排项目
        /// </summary>
        public int Category { get; }

        public bool IsLoadedPackages { get; set; } = true;

        public bool IsRemote { get; }
        public string Code { get; }
        public string Name { get; }

        /// <summary>
        /// 当前启用的版本
        /// </summary>
        public string Version { get; set; }

        public List<string> Versions { get; set; } = new List<string>();
        public string FilePath { get; set; }
        public string ParametersPath => Path.Combine(FilePath, "parameters.json");

        public string ZipFilePath
        {
            get
            {
                return FilePath + ".zip";
            }
        }

        public List<string> FilePaths { get; set; } = new List<string>();

        public string Description { get; }

        public List<string> Tags { get; }

        public string ParentId { get; }

        public string FileHash { get; }

        public string FileSize { get; }

        public RobotProject(string id, string projectContentId, bool isRemote, string code, string name, string version, string filePath, string description, List<string> tags, int category, string parentId, string fileHash, string fileSize = "")
        {
            Id = id;
            ProjectContentId = projectContentId;
            IsRemote = isRemote;
            Code = code == null ? string.Empty : code;
            Name = name;
            Version = version;
            FilePath = filePath == null ? string.Empty : filePath;
            Description = description;
            Tags = tags;
            Category = category;
            ParentId = parentId;
            FileHash = fileHash;
            FileSize = fileSize;
        }

        public bool IsSingleProject()
        {
            if (string.IsNullOrEmpty(ParentId))
            {
                return true;
            }

            return false;
        }
        
        public List<ArgumentModel> GetArguments()
        {
            var res = GetArgumentModels();
            if (res == null)
            {
                return new List<ArgumentModel>();
            }
            List<RunArgumentModel>? storageArguments = new List<RunArgumentModel>();
            if (File.Exists(ParametersPath))
            {
                try
                {
                    string parameterString = File.ReadAllText(ParametersPath);
                    storageArguments =
                        JsonConvert.DeserializeObject<List<RunArgumentModel>>(parameterString) ?? new();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"参数文件读取异常:{ex}");
                }
            }

            foreach (var item in res)
            {
                if (storageArguments != null )
                {
                    var storeArg = storageArguments.FirstOrDefault(d => d.Name.Equals(item.ArgumentValue));
                    if (storeArg != null)
                    {
                        item.ArgumentValue = storeArg.Value;
                    }
                }
            }

            return res;
        }
        private List<ArgumentModel>? GetArgumentModels()
        {
            string projectFilePath = Path.Combine(FilePath, "project.json");
            string projectJson = File.ReadAllText(projectFilePath);
            ProjectModel? projectModel = JsonConvert.DeserializeObject<ProjectModel>(projectJson);
            return projectModel?.ArgumentsValues;
        }

        public Dictionary<string, string> GetKvbArguments()
        {
            Dictionary<string, string> kvb = new Dictionary<string, string>();
            var arguments =    GetArguments();
            foreach (var argumentModel in arguments)
            {
                kvb.Add(argumentModel.ArgumentName, argumentModel.ArgumentValue==null? string.Empty: argumentModel.ArgumentValue.ToString());
            }

            return kvb;
        }
}