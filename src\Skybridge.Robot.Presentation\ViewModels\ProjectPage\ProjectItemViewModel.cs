﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using FluentAvalonia.UI.Controls;
using Newtonsoft.Json;
using ReactiveUI;
using Skybridge.Controls;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs.Robot.Requests;
using Skybridge.Robot.Application.DTOs.Robot.Responses;
using Skybridge.Robot.Application.DTOs.Task.Responses;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Presentation.Services;
using Skybridge.Robot.Presentation.Views;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage
{
    public class ProjectItemViewModel : ViewModelBase
    {
        
        //服务器下班的版本id
        public string ProjectContentId { get; }

        //服务器下发的id

        public string ProjectId{ get; }
        
        private bool _IsShow;

        public bool IsShow
        {
            get => _IsShow;
            set => this.RaiseAndSetIfChanged(ref _IsShow, value);
        }

        private bool _IsShowRun;
        
        
        private bool _isRunning;

        /// <summary>
        /// 执行状态变更需要修改这个值
        /// </summary>
        public bool IsRunning
        {
            get => _isRunning;
            set => this.RaiseAndSetIfChanged(ref _isRunning, value);
        }

        public bool IsShowBtn
        {
            get => _IsShowRun;
            set => this.RaiseAndSetIfChanged(ref _IsShowRun, value);
        }

        private string _name = string.Empty;

        public string Name
        {
            get => _name;
            set => this.RaiseAndSetIfChanged(ref _name, value);
        }

        private string _version;

        public string Version
        {
            get => _version;
            set
            {
                if (value != _version)
                {
                    this.RaiseAndSetIfChanged(ref _version, value);
                    _robotProject.Version = value;
                }
            }
        }

        private bool _isShowRun;

        public bool IsShowRun
        {
            get => _isShowRun;
            set => this.RaiseAndSetIfChanged(ref _isShowRun, value);
        }

        private ObservableCollection<string> _versions;

        public ObservableCollection<string> Versions
        {
            get => _versions;
            set => this.RaiseAndSetIfChanged(ref _versions, value);
        }
        private ObservableCollection<RunArgumentViewModel> _runArguments = new();

        public ObservableCollection<RunArgumentViewModel> RunArguments
        {
            get => _runArguments;
            set => this.RaiseAndSetIfChanged(ref _runArguments, value);
        }

        private bool openFlyout = false;

        private readonly IProjectManager _projectManager;
        private readonly IExecManager _execManager;
        private readonly ITaskManager _taskManager;
        private readonly RobotContext _robotContext;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RobotProject _robotProject;
        private readonly DialogService _dialogService;
        private readonly RunningContext _runningContext;
        private readonly ILogService _logService;
        private readonly RobotConfig _robotConfig;
        private readonly IScheduleManager _scheduleManager;
        private readonly ScheduleSetting _scheduleSetting;

        public ProjectItemViewModel(RobotProject robotProject, IProjectManager projectManager,
            IExecManager execManager, ITaskManager taskManager, RobotContext robotContext,
            IBusinessRequestService businessRequestService, DialogService dialogService,
            RunningContext runningContext, ILogService logService,
            RobotConfig robotConfig,IScheduleManager scheduleManager,
            ScheduleSetting scheduleSetting)
        {
            _robotProject = robotProject;
            _businessRequestService = businessRequestService;
            _robotContext = robotContext;
            _projectManager = projectManager;
            _execManager = execManager;
            _taskManager = taskManager;
            _dialogService = dialogService;
            _runningContext = runningContext;
            _logService = logService;
            _robotConfig = robotConfig;
            _scheduleManager = scheduleManager;
            _scheduleSetting = scheduleSetting;
            ProjectId = robotProject.Id;
            if (!_robotConfig.IsRemote)
            {
                Versions = new ObservableCollection<string>(_projectManager.GetVersions(ProjectId));
            }
            ToggleShowRunCommand = ReactiveCommand.CreateFromTask(StartTask);
            CloseFlyoutCommand = ReactiveCommand.Create(CloseFlyout);
            OpenFlyoutCommand = ReactiveCommand.Create(OpenFlyout);
            ShowBtnCommand = ReactiveCommand.Create(ShowBtn);
            HiddenBtnCommand = ReactiveCommand.Create(HiddenBtn);
            DelProjectCommand = ReactiveCommand.CreateFromTask(DelProject);
            SetSchedulerCommand = ReactiveCommand.CreateFromTask(SetScheduler);
            _runningContext.OnProjectStarted += (pId) =>
            {
                if (pId.Equals(ProjectId))
                {
                    IsRunning = true;
                }
            };
            _runningContext.OnProjectCompleted += (string projectId) =>
            {
                if (projectId.Equals(ProjectId))
                {
                    IsRunning = false;
                }
            };
            Name = robotProject.Name;
            Version = robotProject.Version;

            
        }

        private async Task SetScheduler()
        {
            var view = new ScheduleListView();
            var viewModel = new ScheduleListViewModel(_robotProject,
                _scheduleSetting ,
                _scheduleManager, 
                _dialogService, 
                _robotContext);
            var cd = new ContentDialog()
            {
                Title = "调度计划列表",
                Content = view , 
                DataContext = viewModel,
                CloseButtonText = "关闭"
            };
            await cd.ShowAsync();
        }

        // public ReactiveCommand<Unit, Unit> ShowTasksCommand => ReactiveCommand.Create(ShowTasks);
        public ReactiveCommand<Unit, Unit> ToggleShowRunCommand { get; }

        public ReactiveCommand<Unit, Unit> CloseFlyoutCommand { get; }
        public ReactiveCommand<Unit, Unit> OpenFlyoutCommand { get; }
        public ReactiveCommand<Unit, Unit> ShowBtnCommand { get; }
        public ReactiveCommand<Unit, Unit> HiddenBtnCommand { get; }
        public ReactiveCommand<Unit, bool> DelProjectCommand { get; }
        
        public ReactiveCommand<Unit,Unit> SetSchedulerCommand { get; }
        public List<string> Tags { get; internal set; }

        // private void ShowTasks()
        // {
        // var mainViewModel = Ioc.Default.GetRequiredService<MainWindowViewModel>();
        // mainViewModel.SelectedItem = mainViewModel.Routers.First(x => x.Name == "任务");
        // }


        private async Task StartTask()
        {
            RunArguments.Clear();
            if (_robotContext.IsRemote)
            {
                await CreateRemoteTask();
            }
            else
            {
                await CreateLocalTask();
            }
        }

        private async Task CreateLocalTask()
        {
            var robotProject = _runningContext.RobotProjects.Find(d => d.Id.Equals(_robotProject.Id));
            if (robotProject == null)
            {
                return;
            }
            if ( !Directory.Exists(robotProject.FilePath))
            {
                await _dialogService.ShowConfirmAsync("提醒",$"项目文件已经丢失，该项目将被清理" );
                _projectManager.DelProject(robotProject.Id);
                return;
            }
            var filePath = robotProject.FilePaths.Find(d => d.Contains(robotProject.Version));
            if (filePath == null)
            {
                await _dialogService.ShowMessageAsync("提示",$"未找到可执行项目文件{filePath}");
                return;
            }
            robotProject.FilePath = filePath;
            var arguments = robotProject.GetArguments();
            _runArguments = GetLocalArguments(arguments);
            var view = new ParameterView();
            var viewModel = new ParameterViewModel(_runArguments);
            var cd = new ContentDialog()
            {
                Title = "启动参数",
                Content = view,
                DataContext = viewModel,
                PrimaryButtonText = "确定",
                SecondaryButtonText = "取消"
            };
            var cdRes = await cd.ShowAsync();
            if (cdRes == ContentDialogResult.Primary)
            {
                IDictionary<string, object> kvbArguments = new Dictionary<string, object>();
                foreach (var argument in _runArguments)
                {
                    kvbArguments.Add(argument.Name, argument.Value);
                }
                bool isDebugLog = viewModel.IsLogDebug;
                var taskId = Guid.NewGuid().ToString();
                List<RunArgumentModel> runArgumentModels = _runArguments.Select(x => new RunArgumentModel
                {
                    Name = x.Name,
                    Value = x.Value,
                }).ToList();
                string parameters = JsonConvert.SerializeObject(runArgumentModels);
                await File.WriteAllTextAsync(robotProject.ParametersPath, parameters);
                TaskInfo taskInfo = new TaskInfo()
                {
                    Category = robotProject.Category,
                    Code = robotProject.Code.ToLower(),
                    Description = robotProject.Description,
                    IsVideoLog = false,
                    LogType = isDebugLog ? "Debug" : "Info",
                    ProjectRoot = robotProject.FilePath,
                    ProjectName = robotProject.Name,
                    Parameters = JsonConvert.SerializeObject(kvbArguments),
                    ProjectId = robotProject.Id,
                    TaskId = taskId,
                    Version = robotProject.Version
                };
                await _robotContext.LocalRunTask(taskInfo);
            }
        }

        
        private ObservableCollection<RunArgumentViewModel> GetLocalArguments(List<ArgumentModel> argumentModels)
        {

            var arguments = 
                argumentModels?.Select(x => new RunArgumentViewModel()
                {
                    Name = x.ArgumentName,
                    Value = x.ArgumentValue?.ToString() ?? "",
                    Type = x.ArgumentType,
                    Annotation = x.Annotation
                });
            return new ObservableCollection<RunArgumentViewModel>(arguments ?? Array.Empty<RunArgumentViewModel>());
        }

        private async Task CreateRemoteTask()
        {
            //获取当前项目的版本信息
            var result = await _businessRequestService.GetActiveProjectInfo(_robotConfig.HttpUrl, _robotProject.Id);
            if (result == null || result.Code != 200 || result.Data == null)
            {
                return;
            }
            RunArguments = GetRemoteArguments(result.Data.Params);
            var view = new ParameterView();
            var viewModel = new ParameterViewModel(RunArguments);
            var cd = new ContentDialog()
            {
                Title = "启动参数",
                Content = view,
                DataContext = viewModel,
                PrimaryButtonText = "确定",
                SecondaryButtonText = "取消"
            };
            var cdRes = await cd.ShowAsync();
            if (cdRes != ContentDialogResult.Primary)
            {
                return;
            }
            //创建任务所需的参数
            var paramDict = RunArguments.ToDictionary(x => x.Name, x => x.Value);
            var @params=JsonConvert.SerializeObject(paramDict);
            CreateTaskRequest request = new CreateTaskRequest();
            request.Rpa = _robotConfig.RobotId;
            request.Params = @params;
            request.Code = result.Data.Code;
            request.IsVedio = viewModel.IsRecord ? "1" : "0";
            request.LogType = viewModel.IsLogDebug ? "DEBUG" : "INFO";
            if (_robotProject.IsSingleProject())
            {
                request.Category = 1;
            }
            else
            {
                request.Category = _robotProject.Category;
            }
            var createTaskRes = await _businessRequestService.CreateTask(_robotConfig.HttpUrl,request);

            if (createTaskRes==null||createTaskRes.Code != 200 ||createTaskRes.Data ==null)
            {
                await _dialogService.ShowMessageAsync($"创建任务失败:服务端请求异常", "错误");
                return;
            }

            var taskInfo = new TaskInfo()
            {
                TaskId = createTaskRes.Data.TaskId,
                Code = request.Code.ToLower(),
                Description = _robotProject.Description,
                IsVideoLog = request.IsVedio == "1",
                LogType = request.LogType,
                ProjectId = _robotProject.Id,
                Parameters = request.Params,
                Category = _robotProject.Category,
                FileHash = _robotProject.FileHash,
                FileSize = _robotProject.FileSize,
                Version = _robotProject.Version,
                ProjectName = _robotProject.Name,
                ProjectContentId = _robotProject.ProjectContentId,
            };
            await _robotContext.RunTask(taskInfo);
        }
        
        public ObservableCollection<RunArgumentViewModel> GetRemoteArguments(List<ParamsInfo> paramsInfos)
        {
            ObservableCollection<RunArgumentViewModel> arguments = new ObservableCollection<RunArgumentViewModel>();
            foreach (var param in paramsInfos)
            {
                RunArgumentViewModel argument = new RunArgumentViewModel()
                {
                    Name = param.Name,
                    Type = param.Type
                };
                arguments.Add(argument);
            }
            return arguments;
        }
        private void CloseFlyout()
        {
            openFlyout = false;
            IsShow = false;
        }

        private void OpenFlyout()
        {
            openFlyout = true;
        }

        private void ShowBtn()
        {
            IsShow = true;
        }

        private void HiddenBtn()
        {
            if (!openFlyout)
                IsShow = false;
        }



        private async Task<bool> DelProject()
        {
            var result = await MessageBox.ShowOverlayAsync("是否删除项目", "提示信息");
            if (result == MessageBoxResult.OK)
            {
                _projectManager.DelProject(_robotProject.Id);
            }
            return result.Equals(MessageBoxResult.OK);
        }
    }
}