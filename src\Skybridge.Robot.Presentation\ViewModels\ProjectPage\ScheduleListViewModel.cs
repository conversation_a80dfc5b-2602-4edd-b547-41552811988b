﻿using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Threading.Tasks;
using Avalonia.Controls.ApplicationLifetimes;
using ReactiveUI;
using Skybridge.Controls;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Presentation.Services;
using Skybridge.Robot.Presentation.Views;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage;

public class ScheduleListViewModel : ViewModelBase
{
    private readonly RobotProject _robotProject;
    private readonly ScheduleSetting _scheduleSetting;
    private readonly IScheduleManager _scheduleManager;
    private readonly DialogService _dialogService;
    private readonly RobotContext _robotContext;
    public ObservableCollection<ScheduleViewModel> Schedules { get; } = new ObservableCollection<ScheduleViewModel>();
    public ReactiveCommand<Unit, Unit> CreateScheduleCommand { get; }
    public ScheduleListViewModel(RobotProject robotProject,
        ScheduleSetting scheduleSetting,
        IScheduleManager scheduleManager,
        DialogService dialogService,
        RobotContext robotContext)
    {
        _robotProject = robotProject;
        _scheduleSetting = scheduleSetting;
        _scheduleManager = scheduleManager;
        _dialogService = dialogService;
        _robotContext = robotContext;
        CreateScheduleCommand = ReactiveCommand.CreateFromTask(CreateSchedule);
        LoadList();
    }

    private void LoadList()
    {
        foreach (var item in _scheduleSetting.ScheduledTaskInfos)
        {
            //只添加与此项目有关的调度计划
            if (item.Value.ProjectId.Equals(_robotProject.Id))
            {
                ScheduleViewModel scheduleViewModel = new ScheduleViewModel(_scheduleSetting,
                    _robotProject,
                    _scheduleManager,
                    _robotContext)
                {
                    TaskName = item.Value.Name,
                    IsEnabled = item.Value.IsEnabled,
                    CronExpression = item.Value.ScheduleRule.CronExpression,
                    SelectedScheduleType = item.Value.ScheduleType,
                };
                Schedules.Add(scheduleViewModel);
            }
        }
    }

    private async Task CreateSchedule()
    {
        var viewModel = new ScheduleViewModel(_scheduleSetting,
            _robotProject,
            _scheduleManager,
            _robotContext);

        // 设置默认任务名称
        viewModel.TaskName = $"执行项目: {_robotProject.Name}";
        viewModel.TaskDescription = $"定时执行项目 {_robotProject.Name})";
        var view = new ScheduleView();
        
        view.DataContext = viewModel;
        var desktop = Avalonia.Application.Current.ApplicationLifetime as IClassicDesktopStyleApplicationLifetime;
        var result = await view.ShowDialog<DialogResult>(desktop.MainWindow);
        // var cdRes = await view();
        if (result == DialogResult.OK)
        {
            try
            {
                // 验证配置
                if (viewModel.HasValidationError)
                {
                    await _dialogService.ShowMessageAsync("配置错误", viewModel.ValidationMessage);
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowMessageAsync("错误", $"设置调度计划失败: {ex.Message}");
            }
        }
    }
    
}