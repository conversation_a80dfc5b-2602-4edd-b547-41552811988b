﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using Avalonia.Controls.ApplicationLifetimes;
using FluentAvalonia.UI.Controls;
using ReactiveUI;
using Skybridge.Controls;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Presentation.Models;
using Skybridge.Robot.Presentation.Services;
using Skybridge.Robot.Presentation.Views;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage;

public class ScheduleListViewModel : ViewModelBase
{
    private readonly RobotProject _robotProject;
    private readonly ScheduleSetting _scheduleSetting;
    private readonly IScheduleManager _scheduleManager;
    private readonly DialogService _dialogService;
    private readonly RobotContext _robotContext;
    public  ScheduleListView? View { get; set; }
    public ObservableCollection<ScheduleViewModel> Schedules { get; } = new ObservableCollection<ScheduleViewModel>();
    public ReactiveCommand<Unit, Unit> CreateScheduleCommand { get; }
    public ReactiveCommand<ScheduleViewModel, Unit> EditScheduleCommand { get; }
    public ReactiveCommand<ScheduleViewModel, Unit> DeleteScheduleCommand { get; }
    public ScheduleListViewModel(RobotProject robotProject,
        ScheduleSetting scheduleSetting,
        IScheduleManager scheduleManager,
        DialogService dialogService,
        RobotContext robotContext)
    {
        _robotProject = robotProject;
        _scheduleSetting = scheduleSetting;
        _scheduleManager = scheduleManager;
        _dialogService = dialogService;
        _robotContext = robotContext;
        CreateScheduleCommand = ReactiveCommand.CreateFromTask(CreateSchedule);
        EditScheduleCommand = ReactiveCommand.CreateFromTask<ScheduleViewModel>(EditSchedule);
        DeleteScheduleCommand = ReactiveCommand.CreateFromTask<ScheduleViewModel>(DeleteSchedule);
        LoadList();
    }

    private void LoadList()
    {
        Schedules.Clear();
        foreach (var item in _scheduleSetting.ScheduledTaskInfos)
        {
            //只添加与此项目有关的调度计划
            if (item.Value.ProjectId.Equals(_robotProject.Id))
            {
                ScheduleViewModel scheduleViewModel = new ScheduleViewModel(
                    DocumentMode.Edit,
                    _scheduleSetting,
                    _robotProject,
                    _scheduleManager,
                    _robotContext)
                {
                    TaskId = item.Key,
                    TaskName = item.Value.Name,
                    IsEnabled = item.Value.IsEnabled,
                    CronExpression = item.Value.ScheduleRule.CronExpression,
                    SelectedScheduleType = item.Value.ScheduleType,
                    TaskDescription = item.Value.Description,
                    IsDebugLog = item.Value.IsLogDebug,
                    RunArgumentViewModels = new ObservableCollection<RunArgumentViewModel>(item.Value.KvbParameters.Select(d=>new RunArgumentViewModel()
                    {
                        Name = d.Key,
                        Value = d.Value
                    }))
                };

                // Load schedule rule details
                if (item.Value.ScheduleRule != null)
                {
                    scheduleViewModel.LoadFromScheduleRule(item.Value.ScheduleRule);
                }

                Schedules.Add(scheduleViewModel);
            }
        }
    }

    private async Task CreateSchedule()
    {
        var viewModel = new ScheduleViewModel(
            DocumentMode.Create,
            _scheduleSetting,
            _robotProject,
            _scheduleManager,
            _robotContext)
        {
            RunArgumentViewModels = new ObservableCollection<RunArgumentViewModel>(_robotProject.GetKvbArguments().Select(d=>new RunArgumentViewModel()
            {
                Name = d.Key,
                Value = d.Value
            }))
        };

        // 设置默认任务名称
        viewModel.TaskName = $"执行项目: {_robotProject.Name}";
        viewModel.TaskDescription = $"定时执行项目 {_robotProject.Name})";
        var view = new ScheduleView
        {
            DataContext = viewModel
        };
        viewModel.View = view;
        if (View != null)
        {
            var result = await view.ShowDialog<DialogResult>(View);
            // var cdRes = await view();
            if (result == DialogResult.OK)
            {
                try
                {
                    // 验证配置
                    if (viewModel.HasValidationError)
                    {
                        await _dialogService.ShowMessageAsync("配置错误", viewModel.ValidationMessage);
                    }
                    LoadList();
                }
                catch (Exception ex)
                {
                    await _dialogService.ShowMessageAsync("错误", $"设置调度计划失败: {ex.Message}");
                }
            }
        }
    }

    private async Task EditSchedule(ScheduleViewModel scheduleViewModel)
    {
        if (scheduleViewModel == null) return;

        // Create a new view model for editing with current values
        var editViewModel = new ScheduleViewModel(
            DocumentMode.Edit,
            _scheduleSetting,
            _robotProject,
            _scheduleManager,
            _robotContext)
        {
            TaskId = scheduleViewModel.TaskId,
            TaskName = scheduleViewModel.TaskName,
            TaskDescription = scheduleViewModel.TaskDescription,
            IsEnabled = scheduleViewModel.IsEnabled,
            CronExpression = scheduleViewModel.CronExpression,
            SelectedScheduleType = scheduleViewModel.SelectedScheduleType,
            IsDebugLog = scheduleViewModel.IsDebugLog,
            RunArgumentViewModels = scheduleViewModel.RunArgumentViewModels,
            IntervalSeconds = scheduleViewModel.IntervalSeconds,
            DailyHour = scheduleViewModel.DailyHour,
            DailyMinute = scheduleViewModel.DailyMinute,
            HourlyMinute = scheduleViewModel.HourlyMinute,
            StartTime = scheduleViewModel.StartTime,
            EndTime = scheduleViewModel.EndTime,
            HasEndTime = scheduleViewModel.HasEndTime
        };

        var view = new ScheduleView
        {
            DataContext = editViewModel
        };
        editViewModel.View = view;
        if (View == null)
        {
            return;
        }

        var result = await view.ShowDialog<DialogResult>(View);

        if (result == DialogResult.OK)
        {
            try
            {
                // Validation check
                if (editViewModel.HasValidationError)
                {
                    await _dialogService.ShowMessageAsync("配置错误", editViewModel.ValidationMessage);
                    return;
                }
                LoadList();

                await _dialogService.ShowMessageAsync("成功", "调度计划已更新");
            }
            catch (Exception ex)
            {
                await _dialogService.ShowMessageAsync("错误", $"更新调度计划失败: {ex.Message}");
            }
        }
    }

    private async Task DeleteSchedule(ScheduleViewModel scheduleViewModel)
    {
        if (scheduleViewModel == null) return;

        var confirmResult = await _dialogService.ShowConfirmAsync("确认删除",
            $"确定要删除调度计划 '{scheduleViewModel.TaskName}' 吗？此操作不可撤销。");

        if (confirmResult  == ContentDialogResult.Primary)
        {
            try
            {
                // Find and remove the task
                var taskToRemove = _scheduleSetting.ScheduledTaskInfos.FirstOrDefault(x =>
                    x.Value.ProjectId.Equals(_robotProject.Id) &&
                    x.Value.TaskId.Equals(scheduleViewModel.TaskId));

                if (taskToRemove.Key != null)
                {
                    _scheduleManager.RemoveTask(taskToRemove.Key);
                    _scheduleSetting.ScheduledTaskInfos.Remove(taskToRemove.Key);
                    _scheduleSetting.SaveSettings();

                    // Refresh the list
                    LoadList();

                    await _dialogService.ShowMessageAsync("成功", "调度计划已删除");
                }
                else
                {
                    await _dialogService.ShowMessageAsync("错误", "未找到要删除的调度计划");
                }
            }
            catch (Exception ex)
            {
                await _dialogService.ShowMessageAsync("错误", $"删除调度计划失败: {ex.Message}");
            }
        }
    }

}