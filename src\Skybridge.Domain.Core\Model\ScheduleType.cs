namespace Skybridge.Domain.Core.Model;

/// <summary>
/// 调度类型枚举
/// </summary>
public enum ScheduleType
{
    /// <summary>
    /// Cron表达式
    /// </summary>
    Cron,

    /// <summary>
    /// 按秒间隔
    /// </summary>
    Interval,

    /// <summary>
    /// 每日执行
    /// </summary>
    Daily,

    /// <summary>
    /// 每小时执行
    /// </summary>
    Hourly
}

/// <summary>
/// 调度类型项目
/// </summary>
public class ScheduleTypeItem
{
    public ScheduleType Type { get; }
    public string Name { get; }
    public string Description { get; }

    public ScheduleTypeItem(ScheduleType type, string name, string description)
    {
        Type = type;
        Name = name;
        Description = description;
    }

    public override string ToString()
    {
        return Name;
    }
}
