﻿using NCrontab;
using Skybridge.Domain.Core.Model;


namespace Skybridge.Robot.Infrastructure.Models;
/// <summary>
/// 调度规则实现类
/// </summary>
public class ScheduleRule : IScheduleRule
{
    private readonly CrontabSchedule? _cronSchedule;

    public string CronExpression { get; }
    public TimeSpan? Interval { get; }
    public DateTime? StartTime { get; }
    public DateTime? EndTime { get; }

    public ScheduleRule()
    {
        
    }
    
    /// <summary>
    /// 基于Cron表达式的调度规则
    /// </summary>
    /// <param name="cronExpression">Cron表达式</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    public ScheduleRule(string cronExpression, DateTime? startTime = null, DateTime? endTime = null)
    {
        if (string.IsNullOrWhiteSpace(cronExpression))
            throw new ArgumentException("Cron表达式不能为空", nameof(cronExpression));

        try
        {
            _cronSchedule = CrontabSchedule.Parse(cronExpression);
        }
        catch (Exception ex)
        {
            throw new ArgumentException($"无效的Cron表达式: {cronExpression}", nameof(cronExpression), ex);
        }

        CronExpression = cronExpression;
        Interval = null;
        StartTime = startTime;
        EndTime = endTime;
    }

    /// <summary>
    /// 基于时间间隔的调度规则
    /// </summary>
    /// <param name="interval">时间间隔</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    public ScheduleRule(TimeSpan interval, DateTime? startTime = null, DateTime? endTime = null)
    {
        if (interval <= TimeSpan.Zero)
            throw new ArgumentException("时间间隔必须大于0", nameof(interval));

        CronExpression = string.Empty;
        Interval = interval;
        StartTime = startTime;
        EndTime = endTime;
        _cronSchedule = null;
    }

    public DateTime? GetNextExecutionTime(DateTime? lastExecutionTime = null)
    {
        var baseTime = lastExecutionTime ?? DateTime.Now;

        // 如果有结束时间且已过期，返回null
        if (EndTime.HasValue && baseTime >= EndTime.Value)
            return null;

        DateTime? nextTime = null;

        if (_cronSchedule != null)
        {
            // 使用Cron表达式计算下次执行时间
            nextTime = _cronSchedule.GetNextOccurrence(baseTime);
        }
        else if (Interval.HasValue)
        {
            // 使用时间间隔计算下次执行时间
            if (lastExecutionTime.HasValue)
            {
                nextTime = lastExecutionTime.Value.Add(Interval.Value);
            }
            else
            {
                // 首次执行
                nextTime = StartTime ?? DateTime.Now;
            }
        }

        // 确保下次执行时间不早于开始时间
        if (nextTime.HasValue && StartTime.HasValue && nextTime.Value < StartTime.Value)
        {
            if (_cronSchedule != null)
            {
                nextTime = _cronSchedule.GetNextOccurrence(StartTime.Value);
            }
            else
            {
                nextTime = StartTime.Value;
            }
        }

        // 确保下次执行时间不晚于结束时间
        if (nextTime.HasValue && EndTime.HasValue && nextTime.Value >= EndTime.Value)
        {
            return null;
        }

        return nextTime;
    }

    /// <summary>
    /// 验证Cron表达式是否有效
    /// </summary>
    /// <param name="cronExpression">Cron表达式</param>
    /// <returns>是否有效</returns>
    public static bool IsValidCronExpression(string cronExpression)
    {
        if (string.IsNullOrWhiteSpace(cronExpression))
            return false;

        try
        {
            CrontabSchedule.Parse(cronExpression);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 创建每日执行的调度规则
    /// </summary>
    /// <param name="hour">小时 (0-23)</param>
    /// <param name="minute">分钟 (0-59)</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>调度规则</returns>
    public static ScheduleRule CreateDaily(int hour, int minute, DateTime? startTime = null, DateTime? endTime = null)
    {
        if (hour < 0 || hour > 23)
            throw new ArgumentOutOfRangeException(nameof(hour), "小时必须在0-23之间");
        if (minute < 0 || minute > 59)
            throw new ArgumentOutOfRangeException(nameof(minute), "分钟必须在0-59之间");

        var cronExpression = $"{minute} {hour} * * *";
        return new ScheduleRule(cronExpression, startTime, endTime);
    }

    /// <summary>
    /// 创建每小时执行的调度规则
    /// </summary>
    /// <param name="minute">分钟 (0-59)</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>调度规则</returns>
    public static ScheduleRule CreateHourly(int minute, DateTime? startTime = null, DateTime? endTime = null)
    {
        if (minute < 0 || minute > 59)
            throw new ArgumentOutOfRangeException(nameof(minute), "分钟必须在0-59之间");

        var cronExpression = $"{minute} * * * *";
        return new ScheduleRule(cronExpression, startTime, endTime);
    }

    /// <summary>
    /// 创建按秒间隔执行的调度规则
    /// </summary>
    /// <param name="seconds">间隔秒数</param>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>调度规则</returns>
    public static ScheduleRule CreateInterval(int seconds, DateTime? startTime = null, DateTime? endTime = null)
    {
        if (seconds <= 0)
            throw new ArgumentOutOfRangeException(nameof(seconds), "间隔秒数必须大于0");

        return new ScheduleRule(TimeSpan.FromSeconds(seconds), startTime, endTime);
    }

    public override string ToString()
    {
        if (!string.IsNullOrEmpty(CronExpression))
        {
            return $"Cron: {CronExpression}";
        }
        else if (Interval.HasValue)
        {
            return $"Interval: {Interval.Value}";
        }
        return "Invalid Schedule Rule";
    }
}