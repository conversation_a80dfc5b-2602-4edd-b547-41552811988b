using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Controls.Primitives;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Media;
using Skybridge.Domain.Core;
using Skybridge.Robot.Presentation.ViewModels;
using System.Runtime.InteropServices;
using Autofac;
using Autofac.Core;
using Avalonia.Markup.Xaml;

namespace Skybridge.Robot.Presentation
{
    public partial class MainWindow : Window
    {
        private Point _startPosition;
        private bool _isPointerPressed;
        private Grid _titleBar;
        private Button _minimizeButton;
        private Button _maximizeButton;
        private Button _closeButton;

        public MainWindow()
        {
            if (Avalonia.Application.Current is App app)
            {
                IContainer container = app.Container;
                var mainWindowViewModel = container.Resolve<MainWindowViewModel>();
                DataContext = mainWindowViewModel;
            }
            #if DEBUG
            this.AttachDevTools();
            #endif
            AvaloniaXamlLoader.Load(this);
            SetupCustomTitleBar();
        }

        private void SetupCustomTitleBar()
        {
            // Find the titlebar and control buttons from XAML
            _titleBar = this.FindControl<Grid>("TitleBar");
            _minimizeButton = this.FindControl<Button>("MinimizeButton");
            _maximizeButton = this.FindControl<Button>("MaximizeButton");
            _closeButton = this.FindControl<Button>("CloseButton");

            if (_titleBar != null)
            {
                // Set up drag functionality for the titlebar
                _titleBar.PointerPressed += TitleBar_PointerPressed;
                _titleBar.PointerReleased += TitleBar_PointerReleased;
                _titleBar.PointerMoved += TitleBar_PointerMoved;
            }

            // Set up button click events
            if (_minimizeButton != null)
                _minimizeButton.Click += MinimizeButton_Click;

            if (_maximizeButton != null)
                _maximizeButton.Click += MaximizeButton_Click;

            if (_closeButton != null)
                _closeButton.Click += CloseButton_Click;
        }

        private void TitleBar_PointerPressed(object sender, PointerPressedEventArgs e)
        {
            _isPointerPressed = true;
            _startPosition = e.GetPosition(this);

            // Capture the pointer to receive events even when the pointer leaves the control
            e.Pointer.Capture(_titleBar);
        }

        private void TitleBar_PointerReleased(object sender, PointerReleasedEventArgs e)
        {
            _isPointerPressed = false;
            e.Pointer.Capture(null);
        }

        private void TitleBar_PointerMoved(object sender, PointerEventArgs e)
        {
            if (_isPointerPressed)
            {
                Point currentPosition = e.GetPosition(this);
                Position = new PixelPoint(
                    Position.X + (int)(currentPosition.X - _startPosition.X),
                    Position.Y + (int)(currentPosition.Y - _startPosition.Y)
                );
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnApplyTemplate(TemplateAppliedEventArgs e)
        {
            base.OnApplyTemplate(e);
            GlobalService.SetNotificationManager(new WindowNotificationManager(TopLevel.GetTopLevel(this)!)
            {
                MaxItems = 5,
                Margin = new Thickness(0, 0, 0, 60),
                RenderTransform = new ScaleTransform(),
                Position = NotificationPosition.BottomRight
            });
        }

        protected override void OnClosing(WindowClosingEventArgs e)
        {
            e.Cancel = true;
            Hide();
        }
    }
}