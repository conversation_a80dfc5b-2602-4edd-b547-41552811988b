﻿using Skybridge.Domain.Core.Model;

namespace Skybridge.Robot.Infrastructure.Models;

/// <summary>
/// 调度任务信息实现类
/// </summary>
public class ScheduledTaskInfo : IScheduledTaskInfo
{
    public string ProjectId { get; }
    public string TaskId { get; }
    public string Name { get; set; }
    public string Description { get; set; }
    public IScheduleRule ScheduleRule { get; set; }
    
    public ScheduleType ScheduleType { get; }
    public bool IsEnabled { get; set; }
    public DateTime? LastExecutionTime { get; set; }
    public DateTime? NextExecutionTime { get; private set; }
    
    public bool IsLogDebug { get; }
    
    public Dictionary<string, string>? KvbParameters { get; }

    /// <summary>
    /// 任务状态
    /// </summary>
    public ScheduleTaskStatus Status { get; set; }

    /// <summary>
    /// 最后一次执行结果
    /// </summary>
    public string? LastExecutionResult { get; set; }

    /// <summary>
    /// 最后一次执行异常
    /// </summary>
    public Exception? LastExecutionException { get; set; }

    /// <summary>
    /// 任务创建时间
    /// </summary>
    public DateTime CreatedTime { get; }

    /// <summary>
    /// 任务更新时间
    /// </summary>
    public DateTime UpdatedTime { get; set; }

    /// <summary>
    /// 执行次数
    /// </summary>
    public int ExecutionCount { get; set; }

    /// <summary>
    /// 成功执行次数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败执行次数
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="taskId">任务唯一标识</param>
    /// <param name="name">任务名称</param>
    /// <param name="description">任务描述</param>
    /// <param name="scheduleRule">调度规则</param>
    /// <param name="executeAsync">执行逻辑</param>
    /// <param name="isEnabled">是否启用</param>
    public ScheduledTaskInfo(
        string projectId,
        string taskId,
        string name,
        string description,
        IScheduleRule scheduleRule,
        ScheduleType scheduleType, bool isLogDebug, Dictionary<string, string>? kvbParameters, bool isEnabled = true)
    {
        if(string.IsNullOrWhiteSpace(projectId))
            throw new ArgumentNullException("项目Id不能为空",nameof(projectId));
        
        
        if (string.IsNullOrWhiteSpace(taskId))
            throw new ArgumentException("任务ID不能为空", nameof(taskId));
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("任务名称不能为空", nameof(name));
        if (scheduleRule == null)
            throw new ArgumentNullException(nameof(scheduleRule));
        ProjectId = projectId;
        TaskId = taskId;
        Name = name;
        Description = description ?? string.Empty;
        ScheduleRule = scheduleRule;
        ScheduleType = scheduleType;
        IsLogDebug = isLogDebug;
        KvbParameters = kvbParameters;
        IsEnabled = isEnabled;
        Status = ScheduleTaskStatus.NotStarted;
        CreatedTime = DateTime.Now;
        UpdatedTime = DateTime.Now;
        ExecutionCount = 0;
        SuccessCount = 0;
        FailureCount = 0;
        // 计算下次执行时间
        UpdateNextExecutionTime();
    }

    /// <summary>
    /// 更新下次执行时间
    /// </summary>
    public void UpdateNextExecutionTime()
    {
        if (IsEnabled && Status != ScheduleTaskStatus.Stopped)
        {
            NextExecutionTime = ScheduleRule.GetNextExecutionTime(LastExecutionTime);
        }
        else
        {
            NextExecutionTime = null;
        }
        UpdatedTime = DateTime.Now;
    }

    /// <summary>
    /// 记录执行开始
    /// </summary>
    public void RecordExecutionStart()
    {
        Status = ScheduleTaskStatus.Executing;
        ExecutionCount++;
        UpdatedTime = DateTime.Now;
    }

    /// <summary>
    /// 记录执行成功
    /// </summary>
    /// <param name="result">执行结果</param>
    public void RecordExecutionSuccess(string? result = null)
    {
        LastExecutionTime = DateTime.Now;
        LastExecutionResult = result;
        LastExecutionException = null;
        SuccessCount++;
        Status = IsEnabled ? ScheduleTaskStatus.Running : ScheduleTaskStatus.Paused;
        
        // 更新下次执行时间
        UpdateNextExecutionTime();
        UpdatedTime = DateTime.Now;
    }

    /// <summary>
    /// 记录执行失败
    /// </summary>
    /// <param name="exception">异常信息</param>
    public void RecordExecutionFailure(Exception exception)
    {
        LastExecutionTime = DateTime.Now;
        LastExecutionException = exception;
        LastExecutionResult = $"执行失败: {exception.Message}";
        FailureCount++;
        Status = ScheduleTaskStatus.Error;
        
        // 即使失败也要更新下次执行时间
        UpdateNextExecutionTime();
        UpdatedTime = DateTime.Now;
    }

    /// <summary>
    /// 启用任务
    /// </summary>
    public void Enable()
    {
        IsEnabled = true;
        if (Status == ScheduleTaskStatus.Paused || Status == ScheduleTaskStatus.NotStarted)
        {
            Status = ScheduleTaskStatus.Running;
        }
        UpdateNextExecutionTime();
    }

    /// <summary>
    /// 禁用任务
    /// </summary>
    public void Disable()
    {
        IsEnabled = false;
        if (Status == ScheduleTaskStatus.Running)
        {
            Status = ScheduleTaskStatus.Paused;
        }
        NextExecutionTime = null;
        UpdatedTime = DateTime.Now;
    }

    /// <summary>
    /// 停止任务
    /// </summary>
    public void Stop()
    {
        IsEnabled = false;
        Status = ScheduleTaskStatus.Stopped;
        NextExecutionTime = null;
        UpdatedTime = DateTime.Now;
    }

    /// <summary>
    /// 重置任务状态
    /// </summary>
    public void Reset()
    {
        LastExecutionTime = null;
        LastExecutionResult = null;
        LastExecutionException = null;
        ExecutionCount = 0;
        SuccessCount = 0;
        FailureCount = 0;
        Status = ScheduleTaskStatus.NotStarted;
        UpdateNextExecutionTime();
    }

    /// <summary>
    /// 检查是否应该执行
    /// </summary>
    /// <returns>是否应该执行</returns>
    public bool ShouldExecute()
    {
        return IsEnabled && 
               Status != ScheduleTaskStatus.Stopped && 
               Status != ScheduleTaskStatus.Executing &&
               NextExecutionTime.HasValue && 
               DateTime.Now >= NextExecutionTime.Value;
    }

    /// <summary>
    /// 获取任务摘要信息
    /// </summary>
    /// <returns>任务摘要</returns>
    public string GetSummary()
    {
        return $"任务: {Name} | 状态: {Status} | 执行次数: {ExecutionCount} | 成功: {SuccessCount} | 失败: {FailureCount} | 下次执行: {NextExecutionTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}";
    }

    public override string ToString()
    {
        return $"{Name} ({TaskId}) - {Status}";
    }
    
    
    public ScheduleTaskDto ToScheduleTaskDto()
    {
        return new ScheduleTaskDto()
        {
            ProjectId = ProjectId,
            TaskId = TaskId,
            Name = Name,
            Description = Description,
            ScheduleRule = (ScheduleRule)ScheduleRule,
            ScheduleType = ScheduleType,
            IsEnabled = IsEnabled,
            LastExecutionTime = LastExecutionTime,
            IsLogDebug = IsLogDebug,
            KvbParameters = KvbParameters,
            FailureCount = FailureCount,
            SuccessCount = SuccessCount,
            ExecutionCount = ExecutionCount,
            UpdatedTime = UpdatedTime,
            CreatedTime = CreatedTime,
            LastExecutionException = LastExecutionException,
            LastExecutionResult = LastExecutionResult,
            Status = Status
        };
    }
}