﻿using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using FluentAvalonia.UI.Windowing;
using Skybridge.Controls;

namespace Skybridge.Robot.Presentation.Views;

public partial class ScheduleView : AppWindow
{
    public ScheduleView()
    {

        InitializeComponent();
    }

    private void CancelButton_Click(object? sender, RoutedEventArgs e)
    {
        Close(DialogResult.Cancel);
    }

}