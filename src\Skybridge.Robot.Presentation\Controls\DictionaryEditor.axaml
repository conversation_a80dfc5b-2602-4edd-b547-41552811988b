<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="300"
             xmlns:vm="clr-namespace:Skybridge.Robot.Presentation.ViewModels.ProjectPage"
             x:Class="Skybridge.Robot.Presentation.Controls.DictionaryEditor"
             x:DataType="vm:ScheduleViewModel">

    <UserControl.Styles>
        <!-- Modern Button Styles -->
        <Style Selector="Button.add-button">
            <Setter Property="Background" Value="#10B981"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>
        
        <Style Selector="Button.delete-button">
            <Setter Property="Background" Value="#EF4444"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>
        
        <Style Selector="Button.delete-button:pointerover">
            <Setter Property="Background" Value="#DC2626"/>
        </Style>
        
        <!-- Modern TextBox Style -->
        <Style Selector="TextBox.modern">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D1D5DB"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="MinHeight" Value="32"/>
        </Style>
        
        <Style Selector="TextBox.modern:focus">
            <Setter Property="BorderBrush" Value="#3B82F6"/>
        </Style>
        
        <!-- DataGrid Styling -->
        <Style Selector="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        </Style>
        
        <Style Selector="DataGridColumnHeader">
            <Setter Property="Background" Value="#F9FAFB"/>
            <Setter Property="Foreground" Value="#374151"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Padding" Value="12,10"/>
            <Setter Property="BorderBrush" Value="#E5E7EB"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
        </Style>
        
        <Style Selector="DataGridRow">
            <Setter Property="MinHeight" Value="40"/>
            <Setter Property="Background" Value="White"/>
        </Style>
        
        <Style Selector="DataGridRow:nth-child(even)">
            <Setter Property="Background" Value="#FAFBFC"/>
        </Style>
        
        <Style Selector="DataGridRow:pointerover">
            <Setter Property="Background" Value="#F3F4F6"/>
        </Style>
        
        <Style Selector="DataGridCell">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </UserControl.Styles>

    <Grid RowDefinitions="Auto,*,Auto">
        <!-- Header -->
        <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,12">
            <TextBlock Grid.Column="0" 
                      Text="键值对参数" 
                      FontSize="14" 
                      FontWeight="SemiBold" 
                      Foreground="#374151"
                      VerticalAlignment="Center"/>
        </Grid>

        <!-- DataGrid for Key-Value Pairs -->
        <DataGrid x:Name="DataGrid" Grid.Row="1" 
                  Focusable="False"
                 ItemsSource="{Binding RunArgumentViewModels}"
                 AutoGenerateColumns="False"
                 CanUserReorderColumns="False"
                 CanUserResizeColumns="True"
                 CanUserSortColumns="True"
                 HeadersVisibility="Column"
                 SelectionMode="Single"
                 MinHeight="150">
            
            <DataGrid.Columns>
                <!-- Key Column -->
                <DataGridTemplateColumn Header="参数名" Width="2*" MinWidth="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBox 
                                IsReadOnly="True"
                                Text="{Binding Name, Mode=TwoWay}" 
                                    Classes="modern"
                                    Watermark="输入参数名"
                                    BorderThickness="0"
                                    Background="Transparent"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <!-- Value Column -->
                <DataGridTemplateColumn Header="参数值" Width="3*" MinWidth="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBox Text="{Binding Value, Mode=TwoWay}" 
                                    Classes="modern"
                                    Watermark="输入参数值"
                                    BorderThickness="0"
                                    Background="Transparent"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Footer Info -->
        <TextBlock Grid.Row="2" 
                  Text="💡 提示：参数名为只读数据，不允许修改。参数值无需加双引号！" 
                  FontSize="12" 
                  Foreground="#6B7280" 
                  Margin="0,12,0,0"/>
    </Grid>
</UserControl>
