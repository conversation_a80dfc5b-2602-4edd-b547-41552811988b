﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ReactiveUI;
using Skybridge.Controls;
using Skybridge.Domain.Core.Model;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.DTOs.Task.Responses;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Infrastructure.Models;
using Skybridge.Robot.Presentation.Controls;
using Skybridge.Robot.Presentation.Models;
using Skybridge.Robot.Presentation.Views;
using Skybridge.Robot.Styles.Services;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage;

public class ScheduleViewModel : ViewModelBase
{


    #region 私有字段

    private readonly DocumentMode _documentMode;
    private readonly ScheduleSetting _scheduleSetting;
    private readonly RobotProject _robotProject;
    private readonly IScheduleManager _scheduleManager;
    private readonly RobotContext _robotContext;
    private string _taskName = "";
    private string _taskDescription = "";
    private ScheduleType _selectedScheduleType;
    private string _cronExpression = "0 0 * * *"; // 默认每天午夜执行
    private int _intervalSeconds = 60;
    private int _dailyHour = 0;
    private int _dailyMinute = 0;
    private int _hourlyMinute = 0;
    private DateTimeOffset _startTime = DateTime.Now;
    private DateTimeOffset? _endTime;
    private bool _hasEndTime = false;
    private bool _isEnabled = true;
    private string _validationMessage = "";
    private bool _hasValidationError = false;
    private int _selectedScheduleTypeIndex;
    private bool _isDebugLog;

    #endregion
    
    #region 界面
    
    public ScheduleView? View { get; set; }
    
    #endregion

    #region 公共属性

    public bool IsDebugLog
    {
        get => _isDebugLog;
        set => this.RaiseAndSetIfChanged(ref _isDebugLog, value);
    }

    public string TaskName
    {
        get => _taskName;
        set => this.RaiseAndSetIfChanged(ref _taskName, value);
    }

    public string TaskDescription
    {
        get => _taskDescription;
        set => this.RaiseAndSetIfChanged(ref _taskDescription, value);
    }

    public ScheduleType SelectedScheduleType
    {
        get => _selectedScheduleType;
        set
        {
            this.RaiseAndSetIfChanged(ref _selectedScheduleType, value);
            ValidateSchedule();
        }
    }
    
    public string CronExpression
    {
        get => _cronExpression;
        set
        {
            this.RaiseAndSetIfChanged(ref _cronExpression, value);
            ValidateSchedule();
        }
    }

    public int IntervalSeconds
    {
        get => _intervalSeconds;
        set
        {
            this.RaiseAndSetIfChanged(ref _intervalSeconds, value);
            ValidateSchedule();
        }
    }

    public int DailyHour
    {
        get => _dailyHour;
        set
        {
            this.RaiseAndSetIfChanged(ref _dailyHour, value);
            ValidateSchedule();
        }
    }

    public int DailyMinute
    {
        get => _dailyMinute;
        set
        {
            this.RaiseAndSetIfChanged(ref _dailyMinute, value);
            ValidateSchedule();
        }
    }

    public int HourlyMinute
    {
        get => _hourlyMinute;
        set
        {
            this.RaiseAndSetIfChanged(ref _hourlyMinute, value);
            ValidateSchedule();
        }
    }

    public DateTimeOffset StartTime
    {
        get => _startTime;
        set => this.RaiseAndSetIfChanged(ref _startTime, value);
    }

    public DateTimeOffset? EndTime
    {
        get => _endTime;
        set => this.RaiseAndSetIfChanged(ref _endTime, value);
    }

    public bool HasEndTime
    {
        get => _hasEndTime;
        set
        {
            this.RaiseAndSetIfChanged(ref _hasEndTime, value);
            if (!value)
            {
                EndTime = null;
            }
            else if (!EndTime.HasValue)
            {
                EndTime = DateTime.Now.AddDays(30); // 默认30天后结束
            }
        }
    }

    public bool IsEnabled
    {
        get => _isEnabled;
        set => this.RaiseAndSetIfChanged(ref _isEnabled, value);
    }

    public string ValidationMessage
    {
        get => _validationMessage;
        set => this.RaiseAndSetIfChanged(ref _validationMessage, value);
    }

    public bool HasValidationError
    {
        get => _hasValidationError;
        set => this.RaiseAndSetIfChanged(ref _hasValidationError, value);
    }

    public ObservableCollection<ScheduleTypeItem> ScheduleTypes { get; }

    public ObservableCollection<int> Hours { get; }
    public ObservableCollection<int> Minutes { get; }
    
    public string TaskId { get; set; }

    #endregion

    #region 命令

    public ReactiveCommand<Unit, Unit> ValidateCommand { get; }
    public ReactiveCommand<Unit, Unit> TestCronCommand { get; }

    public int SelectedScheduleTypeIndex
    {
        get => _selectedScheduleTypeIndex;
        set => this.RaiseAndSetIfChanged(ref _selectedScheduleTypeIndex, value);
    }

    private ObservableCollection<RunArgumentViewModel> _runArgumentViewModels;

    public ObservableCollection<RunArgumentViewModel> RunArgumentViewModels
    {
        get => _runArgumentViewModels;
        set => this.RaiseAndSetIfChanged(ref _runArgumentViewModels, value);
    }
    public ReactiveCommand<Unit,Unit> SaveCommand { get; }
    

    #endregion

    public ScheduleViewModel(
        DocumentMode documentMode,
        ScheduleSetting scheduleSetting,
        RobotProject robotProject,
        IScheduleManager scheduleManager,
        RobotContext robotContext)
    {
        _documentMode = documentMode;
        _scheduleSetting = scheduleSetting;
        _robotProject = robotProject;
        _scheduleManager = scheduleManager;
        _robotContext = robotContext;
        // 初始化调度类型选项
        ScheduleTypes = new ObservableCollection<ScheduleTypeItem>
        {
            new ScheduleTypeItem(ScheduleType.Interval, "按秒间隔", "每隔指定秒数执行一次"),
            new ScheduleTypeItem(ScheduleType.Hourly, "每小时执行", "每小时在指定分钟执行"),
            new ScheduleTypeItem(ScheduleType.Daily, "每日执行", "每天在指定时间执行"),
            new ScheduleTypeItem(ScheduleType.Cron, "Cron表达式", "使用Cron表达式进行复杂的时间调度")
        };
        // 初始化小时和分钟选项
        Hours = new ObservableCollection<int>(Enumerable.Range(0, 24));
        Minutes = new ObservableCollection<int>(Enumerable.Range(0, 60));

        // 初始化命令
        ValidateCommand = ReactiveCommand.Create(ValidateSchedule);
        TestCronCommand = ReactiveCommand.Create(TestCronExpression);
        SaveCommand = ReactiveCommand.Create(SaveSchedule);
        RunArgumentViewModels = new ObservableCollection<RunArgumentViewModel>(_robotProject.GetKvbArguments()
            .Select(d => new RunArgumentViewModel()
            {
                Name = d.Key,
                Value = d.Value
            }));
        // 设置验证
        SetupValidation();
    }

    public void SaveSchedule()
    {
        // 验证配置
        ValidateSchedule();
        if (HasValidationError)
        {
            throw new InvalidOperationException($"配置验证失败: {ValidationMessage}");
        }

        if (_documentMode == DocumentMode.Edit)
        {
            // Find and remove the old task
            var taskToRemove = _scheduleSetting.ScheduledTaskInfos.FirstOrDefault(x =>
                x.Value.ProjectId.Equals(_robotProject.Id) &&
                x.Value.TaskId.Equals(TaskId));

            if (taskToRemove.Key != null)
            {
                _scheduleManager.RemoveTask(taskToRemove.Key);
                _scheduleSetting.ScheduledTaskInfos.Remove(taskToRemove.Key);
            }
        }
        
        // 创建调度规则
        var scheduleRule = CreateScheduleRule();

        if (_documentMode == DocumentMode.Create)
        {
            // 创建任务执行逻辑
            var taskId = $"project_{_robotProject.Id}_{Guid.NewGuid():N}";
            TaskId = taskId;   
        }
        var scheduledTask = new ScheduledTaskInfo(_robotProject.Id,
            TaskId,
            TaskName,
            TaskDescription,
            scheduleRule,
            SelectedScheduleType,
            IsDebugLog,
            RunArgumentViewModels.Select(d=> 
                    new KeyValuePair<string,string>(d.Name,d.Value))
                .ToDictionary(d=>d.Key,d=>d.Value),
            IsEnabled
        );

        // 添加到调度管理器
        _scheduleManager.AddTask(scheduledTask);
        _scheduleSetting.ScheduledTaskInfos.TryAdd(scheduledTask.TaskId, scheduledTask.ToScheduleTaskDto());

        // 如果管理器未运行，启动它
        if (!_scheduleManager.IsManagerRunning())
        {
            _scheduleManager.StartManager();
        }

        _scheduleSetting.SaveSettings();
        View?.Close(DialogResult.OK);
    }
    /// <summary>
    /// 单机调度创建的任务
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <param name="isDebugLog"></param>
    /// <param name="kvbArguments"></param>
    private async Task ExecuteProjectAsync(CancellationToken cancellationToken,bool isDebugLog, Dictionary<string, string>? kvbArguments = null)
    {
        var taskId = Guid.NewGuid().ToString();
        TaskInfo taskInfo = new TaskInfo()
        {
            Category = _robotProject.Category,
            Code = _robotProject.Code.ToLower(),
            Description = _robotProject.Description,
            IsVideoLog = false,
            LogType = isDebugLog ? "Debug" : "Info",
            ProjectRoot = _robotProject.FilePath,
            ProjectName = _robotProject.Name,
            Parameters = JsonConvert.SerializeObject(kvbArguments),
            ProjectId = _robotProject.Id,
            TaskId = taskId,
            Version = _robotProject.Version
        };
        await _robotContext.LocalRunTask(taskInfo);
    }
    #region 验证方法

    private void SetupValidation()
    {
        // 监听属性变化进行验证
        this.WhenAnyValue(x => x.TaskName)
            .Subscribe(_ => ValidateSchedule());

        this.WhenAnyValue(x => x.CronExpression)
            .Subscribe(_ => ValidateSchedule());

        this.WhenAnyValue(x => x.IntervalSeconds)
            .Subscribe(_ => ValidateSchedule());

        this.WhenAnyValue(x => x.DailyHour)
            .Subscribe(_ => ValidateSchedule());

        this.WhenAnyValue(x => x.DailyMinute)
            .Subscribe(_ => ValidateSchedule());

        this.WhenAnyValue(x => x.HourlyMinute)
            .Subscribe(_ => ValidateSchedule());
    }

    private void ValidateSchedule()
    {
        var errors = new List<string>();

        // 验证任务名称
        if (string.IsNullOrWhiteSpace(TaskName))
        {
            errors.Add("任务名称不能为空");
        }

        // 根据调度类型进行验证
        switch (SelectedScheduleType)
        {
            case ScheduleType.Cron:
                ValidateCronExpression(errors);
                break;
            case ScheduleType.Interval:
                ValidateInterval(errors);
                break;
            case ScheduleType.Daily:
                ValidateDaily(errors);
                break;
            case ScheduleType.Hourly:
                ValidateHourly(errors);
                break;
        }

        // 验证时间范围
        if (HasEndTime && EndTime.HasValue && EndTime.Value <= StartTime)
        {
            errors.Add("结束时间必须晚于开始时间");
        }

        // 更新验证状态
        HasValidationError = errors.Any();
        ValidationMessage = HasValidationError ? string.Join("; ", errors) : "配置有效";
    }

    private void ValidateCronExpression(List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(CronExpression))
        {
            errors.Add("Cron表达式不能为空");
            return;
        }

        if (!ScheduleRule.IsValidCronExpression(CronExpression))
        {
            errors.Add("Cron表达式格式无效");
        }
    }

    private void ValidateInterval(List<string> errors)
    {
        if (IntervalSeconds <= 0)
        {
            errors.Add("时间间隔必须大于0秒");
        }
        else if (IntervalSeconds < 1)
        {
            errors.Add("时间间隔不能小于1秒");
        }
    }

    private void ValidateDaily(List<string> errors)
    {
        if (DailyHour < 0 || DailyHour > 23)
        {
            errors.Add("小时必须在0-23之间");
        }

        if (DailyMinute < 0 || DailyMinute > 59)
        {
            errors.Add("分钟必须在0-59之间");
        }
    }

    private void ValidateHourly(List<string> errors)
    {
        if (HourlyMinute < 0 || HourlyMinute > 59)
        {
            errors.Add("分钟必须在0-59之间");
        }
    }

    private void TestCronExpression()
    {
        if (string.IsNullOrWhiteSpace(CronExpression))
        {
            ValidationMessage = "请输入Cron表达式";
            return;
        }

        try
        {
            var rule = new ScheduleRule(CronExpression, StartTime.Date, EndTime?.DateTime);
            var nextExecution = rule.GetNextExecutionTime();

            if (nextExecution.HasValue)
            {
                ValidationMessage = $"下次执行时间: {nextExecution.Value:yyyy-MM-dd HH:mm:ss}";
                HasValidationError = false;
            }
            else
            {
                ValidationMessage = "根据当前配置，任务不会执行";
                HasValidationError = true;
            }
        }
        catch (Exception ex)
        {
            ValidationMessage = $"Cron表达式测试失败: {ex.Message}";
            HasValidationError = true;
        }
    }

    #endregion

    #region 公共方法

    /// <summary>
    /// 创建调度规则
    /// </summary>
    /// <returns>调度规则</returns>
    public IScheduleRule CreateScheduleRule()
    {
        if (HasValidationError)
            throw new InvalidOperationException("当前配置存在验证错误，无法创建调度规则");

        var startTime = StartTime.Date;
        var endTime = HasEndTime ? EndTime?.DateTime : null;

        return SelectedScheduleType switch
        {
            ScheduleType.Cron => new ScheduleRule(CronExpression,startTime,endTime),
            ScheduleType.Interval => ScheduleRule.CreateInterval(IntervalSeconds, startTime, endTime),
            ScheduleType.Daily => ScheduleRule.CreateDaily(DailyHour, DailyMinute, startTime, endTime),
            ScheduleType.Hourly => ScheduleRule.CreateHourly(HourlyMinute, startTime, endTime),
            _ => throw new InvalidOperationException($"不支持的调度类型: {SelectedScheduleType}")
        };
    }

    /// <summary>
    /// 从调度规则加载配置
    /// </summary>
    /// <param name="rule">调度规则</param>
    public void LoadFromScheduleRule(IScheduleRule rule)
    {
        if (rule == null)
            return;

        StartTime = rule.StartTime ?? DateTime.Now;
        EndTime = rule.EndTime;
        HasEndTime = rule.EndTime.HasValue;

        if (!string.IsNullOrEmpty(rule.CronExpression))
        {
            SelectedScheduleType = ScheduleType.Cron;
            CronExpression = rule.CronExpression;
        }
        else if (rule.Interval.HasValue)
        {
            SelectedScheduleType = ScheduleType.Interval;
            IntervalSeconds = (int)rule.Interval.Value.TotalSeconds;
        }
    }
    #endregion
}