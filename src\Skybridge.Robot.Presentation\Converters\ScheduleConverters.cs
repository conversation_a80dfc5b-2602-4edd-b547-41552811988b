using System;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;
using Skybridge.Domain.Core.Model;

namespace Skybridge.Robot.Presentation.Converters;

/// <summary>
/// 枚举相等性转换器
/// </summary>
public class EnumEqualsConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value == null || parameter == null)
            return false;

        // 如果参数是字符串，尝试转换为枚举
        if (parameter is string paramString && value is Enum enumValue)
        {
            if (Enum.TryParse(enumValue.GetType(), paramString, out var parsedEnum))
            {
                return enumValue.Equals(parsedEnum);
            }
        }

        return value.Equals(parameter);
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool boolValue && boolValue && parameter != null)
            return parameter;

        return Avalonia.Data.BindingOperations.DoNothing;
    }
}

/// <summary>
/// 布尔值到颜色转换器
/// </summary>
public class BoolToColorConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool hasError)
        {
            return hasError ? Brushes.Red : Brushes.Green;
        }
        return Brushes.LightGray;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 布尔值到背景色转换器
/// </summary>
public class BoolToBackgroundConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool hasError)
        {
            return hasError ? new SolidColorBrush(Color.FromArgb(30, 255, 0, 0)) : new SolidColorBrush(Color.FromArgb(30, 0, 255, 0));
        }
        return Brushes.Transparent;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 布尔值到前景色转换器
/// </summary>
public class BoolToForegroundConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is bool hasError)
        {
            return hasError ? Brushes.Red : Brushes.Green;
        }
        return Brushes.Black;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// 调度类型转换器
/// </summary>
public class ScheduleTypeConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is ScheduleType scheduleType)
        {
            return new ScheduleTypeItem(scheduleType, GetScheduleTypeName(scheduleType), GetScheduleTypeDescription(scheduleType));
        }
        return null;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is ScheduleTypeItem item)
        {
            return item.Type;
        }
        return ScheduleType.Cron;
    }

    private string GetScheduleTypeName(ScheduleType type)
    {
        return type switch
        {
            ScheduleType.Cron => "Cron表达式",
            ScheduleType.Interval => "按秒间隔",
            ScheduleType.Daily => "每日执行",
            ScheduleType.Hourly => "每小时执行",
            _ => "未知类型"
        };
    }

    private string GetScheduleTypeDescription(ScheduleType type)
    {
        return type switch
        {
            ScheduleType.Cron => "使用Cron表达式进行复杂的时间调度",
            ScheduleType.Interval => "每隔指定秒数执行一次",
            ScheduleType.Daily => "每天在指定时间执行",
            ScheduleType.Hourly => "每小时在指定分钟执行",
            _ => "未知调度类型"
        };
    }
}
