using Autofac;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Infrastructure.ExternalServices;
using Skybridge.Robot.Infrastructure.Services;

namespace Skybridge.Robot.Infrastructure.DependencyInjection;

public class InfrastructureModule : Module
{
    protected override void Load(ContainerBuilder builder)
    {
              
        builder.RegisterType<ScheduleSetting>()
            .AsSelf()
            .SingleInstance();
        
        // 注册日志选项
        builder.RegisterType<LogService>()
            .AsImplementedInterfaces()
            .SingleInstance();

        //上传任务日志存储
        builder.RegisterType<UploadTaskRepository>()
        .AsImplementedInterfaces()
        .SingleInstance();
        
        //工作线程
        builder.RegisterType<WorkingTaskManager>()
            .AsSelf()
            .SingleInstance();
        
        builder.RegisterType<HttpService>()
            .AsImplementedInterfaces()
            .SingleInstance();

        // 注册机器人日志服务
        builder.RegisterType<LostDataRepository>()
            .AsImplementedInterfaces()
            .SingleInstance();
        
        
        builder.RegisterType<RunningContext>()
            .AsSelf()
            .SingleInstance();


        builder.RegisterType<ServiceSetting>()
            .AsSelf()
            .SingleInstance();

        builder.RegisterType<EnvironmentConfig>()
            .AsSelf()
            .SingleInstance();

        builder.RegisterType<BusinessRequestService>()
            .AsImplementedInterfaces()
            .SingleInstance();
        
        builder.RegisterType<RemoteLog>()
            .AsSelf()
            .SingleInstance();
        
        builder.RegisterType<RobotLogService>()
            .AsImplementedInterfaces()
            .SingleInstance();
        


        builder.RegisterType<RobotChannel>()
            .AsImplementedInterfaces()
            .SingleInstance();

        builder.RegisterType<RobotWebServer>()
            .AsImplementedInterfaces()
            .SingleInstance();

        builder.RegisterType<WebSocketClient>()
            .AsImplementedInterfaces()
            .SingleInstance();
  
        
        builder.RegisterType<ProjectManager>()
            .AsImplementedInterfaces()
            .SingleInstance();

        builder.RegisterType<TaskManager>()
            .AsImplementedInterfaces()
            .SingleInstance();

        builder.RegisterType<UploadLogContext>()
            .AsSelf()
            .SingleInstance();

        builder.RegisterType<ExecManager>()
            .AsImplementedInterfaces()
            .SingleInstance();

        builder.RegisterType<ScheduleManager>()
            .AsImplementedInterfaces()
            .SingleInstance();

        builder.RegisterType<RobotContext>()
            .AsSelf()
            .SingleInstance();
        


        builder.RegisterType<LostDataContext>()
            .AsSelf()
            .SingleInstance();
    }
}