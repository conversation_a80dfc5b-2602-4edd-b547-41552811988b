﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using ReactiveUI;
using Skybridge.Domain.Core.Config;
using Skybridge.Domain.Core.Exector;
using Skybridge.Domain.Core.Models;
using Skybridge.Robot.Application.Interfaces;
using Skybridge.Robot.Infrastructure.Configuration;
using Skybridge.Robot.Infrastructure.Contexts;
using Skybridge.Robot.Presentation.Services;

namespace Skybridge.Robot.Presentation.ViewModels.ProjectPage
{
    public class ProjectViewModel : ViewModelBase
    {
        private readonly IProjectManager _projectManager;

        private readonly IExecManager _execManager;

        private readonly ITaskManager _taskManager;
        private readonly RobotContext _robotContext;
        private readonly IBusinessRequestService _businessRequestService;
        private readonly RunningContext _runningContext;
        private readonly DialogService _dialogService;
        private readonly ILogService _logService;
        private readonly UserSetting _userSetting;
        private readonly IScheduleManager _scheduleManager;
        private readonly ScheduleSetting _scheduleSetting;
        private readonly RobotConfig _robotConfig;
        private ObservableCollection<ProjectItemViewModel> _projectItems = new();

        private bool _isRemote;

        private int _scheduleTaskCount;
        
        public int ScheduleTaskCount
        {
            get => _scheduleTaskCount;
            set => this.RaiseAndSetIfChanged(ref _scheduleTaskCount, value);
        }


        public bool IsRemote
        {
            get => _isRemote;
            set => this.RaiseAndSetIfChanged(ref _isRemote, value);
        }

        public ObservableCollection<ProjectItemViewModel> ProjectItems
        {
            get => _projectItems;
            set => this.RaiseAndSetIfChanged(ref _projectItems, value);
        }

        public ProjectViewModel(IProjectManager projectManager,
        IExecManager execManager,
        ITaskManager taskManager,
            RobotContext robotContext,
            IBusinessRequestService businessRequestService,
            RunningContext runningContext,
            DialogService dialogService,
            IAppConfigManager appConfigManager,
            ILogService logService,
            UserSetting userSetting,
        IScheduleManager scheduleManager,
        ScheduleSetting scheduleSetting)
        {
            _businessRequestService = businessRequestService;
            _robotContext = robotContext;
            _projectManager = projectManager;
            _execManager = execManager;
            _taskManager = taskManager;
            _logService = logService;
            _userSetting = userSetting;
            _scheduleManager = scheduleManager;
            _scheduleSetting = scheduleSetting;
            _runningContext = runningContext;
            _dialogService = dialogService;
            _robotConfig = appConfigManager.RobotConfig;
            LoadProject();
            _scheduleManager.OnTaskCountChanged += (count) =>
            {
                ScheduleTaskCount = count;
            };
            appConfigManager.OnChangeMode += (mode) =>
           {
               try
               {
                   ProjectItems.Clear();
                   IsRemote = mode;
                   LoadProject();
               }
               catch (Exception ex)
               {
                   _logService.LogError("项目列表加载失败", ex);
               }
           };
            runningContext.OnProjectAdded += (robotProject) => AddProject(robotProject);
            runningContext.OnProjectUpdated += (robotProject) => UpdateProject(robotProject);
            runningContext.OnProjectDeleted += (projectId) => DeleteProject(projectId);
        }

        private void DeleteProject(string projectId)
        {
            var item = ProjectItems.FirstOrDefault(x => x.ProjectId == projectId);
            if (item != null)
            {
                ProjectItems.Remove(item);
            }
        }

        private void UpdateProject(RobotProject robotProject)
        {
            var projectItem = ProjectItems.FirstOrDefault(d => d.ProjectId.Equals(robotProject.Id));
            if (projectItem != null)
            {
                projectItem.Tags = robotProject.Tags;
                projectItem.Version = robotProject.Version;
                projectItem.Versions = new ObservableCollection<string>(robotProject.Versions);
            }
        }

        private void AddProject(RobotProject robotProject)
        {
            if (!ProjectItems.Any(x => x.ProjectId == robotProject.Id))
            {
                ProjectItems.Add(CreateProjectItemViewModel(robotProject));
            }
        }

        public void LoadProject()
        {
            Task.Run(async () =>
            {
                var result = await _projectManager.GetProjects();
                ProjectItems.Clear();
                if (result == null)
                    return;
                foreach (var item in result)
                {
                    if (_userSetting.VersionMaps.TryGetValue(item.Id, out var map))
                    {
                        item.Version = map;
                    }
                    ProjectItems.Add(CreateProjectItemViewModel(item));
                }
            });
        }

        private ProjectItemViewModel CreateProjectItemViewModel(RobotProject project)
        {
            return new ProjectItemViewModel(project
                    , _projectManager, _execManager, _taskManager, _robotContext,
                    _businessRequestService, _dialogService, _runningContext,
                    _logService, _robotConfig,_scheduleManager,_scheduleSetting);
        }

    }
}